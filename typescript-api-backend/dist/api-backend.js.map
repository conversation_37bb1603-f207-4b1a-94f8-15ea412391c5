{"version": 3, "file": "api-backend.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,gEAAgE;AACzD,MAAM,sBAAsB,GAAG,uBAAuB,CAAC;AACvD,MAAM,uBAAuB,GAAG,yBAAyB,CAAC;AAC1D,MAAM,sBAAsB,GAAG,uBAAuB,CAAC;AACvD,MAAM,8BAA8B,GAAG,+BAA+B,CAAC;AACvE,MAAM,gCAAgC,GAAG,gCAAgC,CAAC;AAEjF,qBAAqB;AACd,MAAM,2BAA2B,GAAG,6BAA6B,CAAC;AAClE,MAAM,mBAAmB,GAAG,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACI5B;AACI;AAEL;AACiD;AAIhB;AAE7D,oCAAoC;AACpC,MAAM,MAAM,GAAG,6CAAM,CAAC;IACpB,OAAO,EAAE,2DAAoB,EAAE;IAC/B,MAAM,EAAE;QACN,QAAQ,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,aAAa;KAC1C;CACF,CAAC,CAAC;AAEH,8BAA8B;AAC9B,MAAM,oBAAoB,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;AAIhD,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IACpC,YAAoB,sBAA8C;QAA9C,2BAAsB,GAAtB,sBAAsB,CAAwB;IAAG,CAAC;IAKhE,KAAD,CAAC,mBAAmB,CACR,KAMd,EACM,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACvF,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,QAAQ;aACf,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAGK,KAAD,CAAC,kBAAkB,CACT,EAAU,EAChB,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAClF,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;aACd,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAGK,KAAD,CAAC,2BAA2B,CACvB,IAAS,EACV,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAEvF,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,+CAA+C;aACzD,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAGK,KAAD,CAAC,qBAAqB,CACjB,IAAS,EACV,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YAEpC,iDAAiD;YACjD,IAAI,WAAW,GAAG,IAAI,CAAC;YACvB,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;gBACjC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACrC;YAED,sDAAsD;YACtD,MAAM,KAAK,GAAI,OAAe,CAAC,KAAK,IAAI,EAAE,CAAC;YAC3C,MAAM,WAAW,GAAsB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC/D,QAAQ,EAAE,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,IAAI;gBACxC,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI;gBACpC,OAAO,EAAE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI;aAClC,CAAC,CAAC,CAAC;YAEJ,2DAA2D;YAC3D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC/E,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC;aAChD;YAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YAE9F,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,sCAAsC;gBAC/C,aAAa,EAAE,WAAW,CAAC,MAAM;aAClC,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAIK,KAAD,CAAC,qBAAqB,CACZ,EAAU,EACf,IAAwC,EACxB,KAA4B,EAC7C,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YAEpC,yBAAyB;YACzB,MAAM,WAAW,GAAsB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAChE,QAAQ,EAAE,IAAI,CAAC,YAAY;gBAC3B,QAAQ,EAAE,IAAI,CAAC,IAAI;gBACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,IAAI,CAAC,MAAM;aACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAET,2DAA2D;YAC3D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,WAAW,CAAC;aACzC;YAED,MAAM,UAAU,GAA2B,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,CAAC;YAC3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;YAE7F,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,sCAAsC;aAChD,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAGK,KAAD,CAAC,qBAAqB,CACZ,EAAU,EAChB,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,IAAI,CAAC,sBAAsB,CAAC,qBAAqB,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;YAErE,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,sCAAsC;aAChD,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;CACF;AAjLO;IADL,wDAAG,CAAC,WAAW,CAAC;IAEd,2EAAW,EAAE;IAOb,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;oEAeP;AAGK;IADL,wDAAG,CAAC,eAAe,CAAC;IAElB,qEAAK,CAAC,IAAI,CAAC;IACX,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;mEAeP;AAGK;IADL,yDAAI,CAAC,kBAAkB,CAAC;IAEtB,oEAAI,EAAE;IACN,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;4EAiBP;AAGK;IADL,yDAAI,CAAC,WAAW,CAAC;IAEf,oEAAI,EAAE;IACN,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;sEAuCP;AAIK;IAFL,wDAAG,CAAC,eAAe,CAAC;IACpB,8DAAS,CAAC,oBAAoB,CAAC;IAE7B,qEAAK,CAAC,IAAI,CAAC;IACX,oEAAI,EAAE;IACN,6EAAa,CAAC,OAAO,CAAC;IACtB,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;sEAgCP;AAGK;IADL,2DAAM,CAAC,eAAe,CAAC;IAErB,qEAAK,CAAC,IAAI,CAAC;IACX,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;sEAgBP;AAtLU,yBAAyB;IAFrC,mEAAc,CAAC,WAAW,CAAC;IAC3B,+CAAO,EAAE;qCAEoC,qFAAsB;GADvD,yBAAyB,CAuLrC;AAvLqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpCqC;AAC1C;AAE4B;AAItD,IAAM,eAAe,GAArB,MAAM,eAAe;IAKpB,KAAD,CAAC,OAAO,CACM,MAAc,EACxB,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC;YAExC,oBAAoB;YACpB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC/B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gBAAgB;iBACxB,CAAC,CAAC;aACJ;YAED,2DAA2D;YAC3D,qEAAqE;YACrE,OAAO,QAAQ,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;oBAC3B,GAAG,EAAE,QAAQ,CAAC,GAAG;iBAClB;aACF,CAAC,CAAC;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;IAGK,KAAD,CAAC,YAAY,CACC,MAAc,EACxB,OAAgB,EAChB,QAAkB;QAEzB,IAAI;YACF,MAAM,OAAO,GAAG,yEAAU,CAAC,OAAO,CAAC,CAAC;YACpC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC;YAExC,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,CAAC,QAAQ,EAAE;gBACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC/B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,gBAAgB;iBACxB,CAAC,CAAC;aACJ;YAED,4CAA4C;YAC5C,QAAQ,CAAC,SAAS,CAAC,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACtD,QAAQ,CAAC,SAAS,CAAC,qBAAqB,EAAE,yBAAyB,QAAQ,CAAC,QAAQ,GAAG,CAAC,CAAC;YACzF,QAAQ,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAExD,8DAA8D;YAC9D,6DAA6D;YAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC,yBAAyB,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;SACpE;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC/B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;SACJ;IACH,CAAC;CACF;AAzEO;IADL,wDAAG,CAAC,UAAU,CAAC;IAEb,qEAAK,CAAC,QAAQ,CAAC;IACf,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;8CAkCP;AAGK;IADL,wDAAG,CAAC,mBAAmB,CAAC;IAEtB,qEAAK,CAAC,QAAQ,CAAC;IACf,mEAAG,EAAE;IACL,mEAAG,EAAE;;;;mDA6BP;AA7EU,eAAe;IAF3B,mEAAc,CAAC,QAAQ,CAAC;IACxB,+CAAO,EAAE;GACG,eAAe,CA8E3B;AA9E2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACP8E;AACzC;AAChC;AAI1B,IAAM,eAAe,GAArB,MAAM,eAAe;IACxB,YAAoB,YAAyB;QAAzB,iBAAY,GAAZ,YAAY,CAAa;IAC7C,CAAC;IAGK,KAAD,CAAC,UAAU,CAAgB,KAAK,EAAS,QAAa;QACvD,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1D,CAAC;IAGK,KAAD,CAAC,WAAW,CAAgB,KAAK,EAAS,QAAa;QACxD,OAAO,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;IAC1D,CAAC;CAEJ;AATS;IADL,wDAAG,CAAC,EAAE,CAAC;IACU,2EAAW,EAAE;IAAS,mEAAG,EAAE;;;;iDAE5C;AAGK;IADL,yDAAI,CAAC,EAAE,CAAC;IACU,2EAAW,EAAE;IAAS,mEAAG,EAAE;;;;kDAE7C;AAZQ,eAAe;IAF3B,mEAAc,CAAC,QAAQ,CAAC;IACxB,+CAAO,EAAE;qCAE2B,2EAAY;GADpC,eAAe,CAc3B;AAd2B;;;;;;;;;;;;;;;;;;ACN4B;AAGxD,MAAM,EAAE,eAAe,EAAE,GAAG,0DAAU,CAAC;AAEvC,iEAAe,IAAI,sDAAM,CACvB,cAAc,EACd;IACE,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,QAAQ;IACpB,MAAM,EAAE,QAAQ;IAChB,SAAS,EAAE,QAAQ;IACnB,UAAU,EAAE,QAAQ;IACpB,mBAAmB,EAAE,QAAQ;IAC7B,WAAW,EAAE,QAAQ;IACrB,OAAO,EAAE,MAAa;IACtB,aAAa,EAAE,QAAQ;IACvB,iBAAiB,EAAE,QAAQ;IAC3B,kBAAkB,EAAE,QAAQ;CAC7B,EACD,EAAE,EACF,EAAE,SAAS,EAAE,kBAAkB,EAAE,EACjC;IACE,UAAU,EAAE;QACV;YACE,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,UAAU,EAAE;gBACV;oBACE,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;oBAChD,YAAY,EAAE,mBAAmB;iBAClC;aACF;SACF;KACF;CACF,CACF,EAAC;;;;;;;;;;;;;;;;;;ACpCyE;AAG3E,MAAM,EAAE,eAAe,EAAE,GAAG,0DAAU,CAAC;AAEvC,iEAAe,IAAI,sDAAM,CACvB,iBAAiB,EACjB;IACE,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,QAAQ;IAChB,kBAAkB,EAAE,QAAQ;IAC5B,WAAW,EAAE,QAAQ;IACrB,SAAS,EAAE,QAAQ;IACnB,SAAS,EAAE,UAAU;IACrB,SAAS,EAAE,QAAQ;IACnB,SAAS,EAAE,UAAU;IACrB,QAAQ,EAAE,QAAQ;IAClB,MAAM,EAAE,QAAQ;IAChB,iBAAiB,EAAE,QAAQ;CAC5B,EACD,EAAE,EACF,EAAE,SAAS,EAAE,aAAa,EAAE,EAC5B;IACE,UAAU,EAAE;QACV;YACE,OAAO,EAAE,CAAC,QAAQ,CAAC;YACnB,UAAU,EAAE;gBACV;oBACE,QAAQ,EAAE,eAAe;oBACzB,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC;oBAChD,YAAY,EAAE,mBAAmB;iBAClC;aACF;SACF;KACF;CACF,CACF,EAAC;;;;;;;;;;;;;;;;;;;;;;;ACpCgE;AAEE;AAEd;AAEJ;AAElD,MAAM,MAAM,GAAG,IAAI,sDAAM,CACvB,QAAQ,EACR;IACE,MAAM,EAAE,QAAQ;IAChB,kBAAkB,EAAE,QAAQ;IAC5B,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,QAAQ;IACpB,QAAQ,EAAE,QAAQ;IAClB,UAAU,EAAE,QAAQ;IACpB,YAAY,EAAE,QAAQ;IACtB,eAAe,EAAE,QAAQ;IACzB,gBAAgB,EAAE,QAAQ;IAC1B,SAAS,EAAE,MAAM;IACjB,MAAM,EAAE;QACN,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,sEAAsB;QACjC,WAAW,EAAE,wEAAwB;QACrC,SAAS,EAAE,IAAI;KAChB;IACD,QAAQ,EAAE;QACR,UAAU,EAAE,aAAa;QACzB,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,yEAAwB;QACnC,WAAW,EAAE,2EAA0B;QACvC,SAAS,EAAE,IAAI;KAChB;IACD,eAAe,EAAE;QACf,UAAU,EAAE,qBAAqB;QACjC,MAAM,EAAE,QAAQ;QAChB,SAAS,EAAE,gFAA+B;QAC1C,WAAW,EAAE,kFAAiC;QAC9C,SAAS,EAAE,IAAI;KAChB;IACD,SAAS,EAAE,QAAQ;IACnB,WAAW,EAAE,QAAQ;CACtB,EACD,EAAE,EACF;IACE,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE;QACL,GAAG,EAAE,QAAQ;QACb,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,EAAE,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;KAC/D;CACF,EACD,EAAE,CACH,CAAC;AAEF,iEAAe,MAAM,EAAC;;;;;;;;;;;;;;;;;;;;ACxDS;AAW/B;;;GAGG;AACI,SAAS,YAAY,CAC1B,GAAY,EACZ,KAAa;IAEb,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,CAAC,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC,CAAC;YACjE,OAAO;SACR;QAED,mCAAmC;QACnC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAE9B,4CAA4C;QAC5C,gDAAgD;QAChD,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,KAAK,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChC,mDAAmD;YACnD,MAAM,OAAO,GAAG,iBAAiB,MAAM,EAAE,CAAC;YAC1C,QAAQ,GAAG,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;SACvC;QAED,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,CAAC;YACd,OAAO;SACR;QAED,0DAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAClE,IAAI,GAAG,EAAE;gBACP,IACE,GAAG,YAAY,uEAAqB;oBACpC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EACxC;oBACA,OAAO,CAAC,IAAI,CAAC,CAAC;iBACf;qBAAM;oBACL,MAAM,CAAC,GAAG,CAAC,CAAC;iBACb;aACF;iBAAM;gBACL,OAAO,CAAC,OAAuB,CAAC,CAAC;aAClC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,KAAK;IACZ,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,aAAa,CAAC;AAC3C,CAAC;AAEM,SAAS,SAAS,CAAC,GAAY;IACpC,OAAO,KAAK,EAAE;QACZ,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,WAAW;QACjE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC;AACrC,CAAC;AACD,SAAS,UAAU,CACjB,YAAoB,EACpB,GAAW;IAEX,IAAI,CAAC,YAAY;QAAE,OAAO,IAAI,CAAC;IAE/B,qBAAqB;IACrB,MAAM,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAE7D,8CAA8C;IAC9C,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;IAC5D,IAAI,CAAC,MAAM;QAAE,OAAO,IAAI,CAAC;IAEzB,2BAA2B;IAC3B,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;AACtC,CAAC;AAED;;;GAGG;AACI,SAAS,mBAAmB,CAAC,GAAY;;IAC9C,gDAAgD;IAChD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;IAC7C,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;QAClD,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;KAChC;IAED,2CAA2C;IAC3C,IAAI,GAAG,CAAC,aAAa,EAAE;QACrB,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,OAAO,GAAG,iBAAiB,MAAM,EAAE,CAAC;QAE1C,IAAI,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;YAC9B,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;SACnC;KACF;IAED,8CAA8C;IAC9C,MAAM,WAAW,GAAG,eAAG,CAAC,OAAO,CAAC,MAAM,0CAClC,KAAK,CAAC,GAAG,EACV,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,0CAC9C,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;IAElB,OAAO,WAAW,CAAC;AACrB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AC5GyB;AAC1B,kFAAkF;AAC3B;AAC0B;AAEjF;;;GAGG;AACH,KAAK,UAAU,sBAAsB;IACnC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,kDAAkD;QAClD,MAAM,UAAU,GAAG;YACjB,KAAK,EAAE;gBACL,EAAE,EAAE,iBAAiB,IAAI,CAAC,GAAG,EAAE,EAAE;gBACjC,GAAG,EAAE;oBACH,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;oBAC/F,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;oBACnG,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;oBAChG,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;oBACjG,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;oBACjG,OAAO,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;iBAC/F;gBACR,QAAQ,EAAE,CAAC;gBACX,mBAAmB,EAAE,CAAC;gBACtB,MAAM,EAAE,CAAC;gBACT,QAAQ,EAAE,sBAAsB;gBAChC,UAAU,EAAE,eAAe;gBAC3B,KAAK,EAAE,eAAe;aACvB;YACD,gBAAgB,EAAE,EAAE;SACF,CAAC;QAErB,kEAAc,CAAC,UAAU,EAAE,KAAK,EAAE,UAAuB,EAAE,EAAE;YAC3D,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YAE3D,0CAA0C;YAC1C,MAAM,WAAW,GAAG,IAAI,GAAG,EAAe,CAAC;YAE3C,oEAAoE;YACpE,MAAM,WAAW,GAAa;gBAC5B,UAAU;gBACV,KAAK,EAAE;oBACL,IAAI,EAAE,KAAK,EAAE,GAAW,EAAE,MAA0B,EAAE,EAAE;wBACtD,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;4BACxB,OAAO,CAAC,GAAG,CAAC,qBAAqB,GAAG,EAAE,CAAC,CAAC;4BACxC,OAAO,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;yBAC7B;wBACD,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;wBACzC,MAAM,MAAM,GAAG,MAAM,MAAM,EAAE,CAAC;wBAC9B,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;wBAC7B,OAAO,MAAM,CAAC;oBAChB,CAAC;oBACD,UAAU,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;wBAChC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC1B,CAAC;iBACK;gBACR,QAAQ,EAAE,CAAC;gBACX,MAAM,EAAE,CAAC;gBACT,QAAQ,EAAE,sBAAsB;gBAChC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,GAAG;aACnB,CAAC;YAET,OAAO,CAAC,WAAW,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,uDAAuD,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAmBD;;;GAGG;AACI,MAAM,mBAAmB,GAAG,KAAK,EACtC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;IAEpC,uDAAuD;IACvD,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;IAC/C,IAAI,CAAC,YAAY,EAAE;QACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,mDAAmD;SAC3D,CAAC,CAAC;QACH,OAAO;KACR;IAED,iCAAiC;IACjC,MAAM,MAAM,GAAG,yDAAS,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,OAAO,GAAG,iBAAiB,MAAM,EAAE,CAAC;IAE1C,mCAAmC;IACnC,IAAI,GAAG,CAAC,aAAa,IAAI,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;QACnD,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;KAClD;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,8BAA8B,GAAG,KAAK,EACjD,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;IAEpC,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,cAAc,KAAK,WAAW,EAAE;QACvD,MAAM,MAAM,GAAG,yDAAS,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,aAAa,GAAG,yBAAyB,MAAM,EAAE,CAAC;QAExD,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG,CAAC,CAAC,CAC/B,GAAG,CAAC,aAAa,IAAI,GAAG,CAAC,aAAa,CAAC,aAAa,CAAC,CACtD,CAAC;KACH;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,sBAAsB,GAAG,KAAK,EACzC,GAAY,EACZ,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;IAEpC,+DAA+D;IAC/D,MAAM,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,IAAI,mEAAmB,CAAC,GAAG,CAAC,CAAC;IAC9D,IAAI,KAAK,EAAE;QACT,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;QAE5B,IAAI;YACF,MAAM,OAAO,GAAG,MAAM,4DAAY,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;gBAC7B,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACtC,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;gBAC1C,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;gBAC9C,GAAG,CAAC,SAAS,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;gBAChE,IAAI,OAAO,CAAC,QAAQ,EAAE;oBACpB,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;iBAC3C;aACF;SACF;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;YACtC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,8BAA8B;aACtC,CAAC,CAAC;YACH,OAAO;SACR;KACF;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAGF;;;GAGG;AACI,MAAM,UAAU,GAAG,KAAK,EAC7B,GAAY,EACZ,IAAc,EACd,IAAkB,EACH,EAAE;IACjB,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,EAAE,CAAC;IAEpC,IAAI;QACF,0BAA0B;QAC1B,MAAM,MAAM,GAAG,yDAAS,CAAC,GAAG,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,MAAM,CAAC,CAAC;QAEpD,6CAA6C;QAC7C,MAAM,WAAW,GAAG,MAAM,sBAAsB,EAAE,CAAC;QAEnD,wCAAwC;QACxC,MAAM,OAAO,GAAG,MAAM,oEAAa,CAAC,WAAW,CAAC,CAAC;QACjD,OAAO,CAAC,GAAG,CAAC,UAAU,OAAO,CAAC,MAAM,8BAA8B,CAAC,CAAC;QAEpE,wBAAwB;QACxB,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC;QAExD,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE,EAAE,gBAAgB;YAC1D,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,wBAAwB,MAAM,CAAC,QAAQ,SAAS,MAAM,CAAC,EAAE,iBAAiB,MAAM,EAAE,CAAC,CAAC;SACjG;aAAM;YACL,0CAA0C;YAC1C,MAAM,gBAAgB,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC;YACrD,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;YAC1C,OAAO,CAAC,GAAG,CAAC,sCAAsC,MAAM,+BAA+B,gBAAgB,EAAE,CAAC,CAAC;SAC5G;KAEF;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QAEpD,mDAAmD;QACnD,MAAM,gBAAgB,GAAG,GAAG,CAAC,SAAS,CAAC,QAAQ,IAAI,CAAC,CAAC;QACrD,GAAG,CAAC,SAAS,CAAC,QAAQ,GAAG,gBAAgB,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,oCAAoC,gBAAgB,EAAE,CAAC,CAAC;KACrE;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CACzB,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;;IACR,IAAI,CAAC,UAAG,CAAC,SAAS,0CAAE,MAAM,GAAE;QAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC,CAAC;QAC3D,OAAO;KACR;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,aAAa,GAAG,CAC3B,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;;IACR,IAAI,CAAC,UAAG,CAAC,SAAS,0CAAE,QAAQ,GAAE;QAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,CAAC;QAClD,OAAO;KACR;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;AC1Q+E;AAGjF;;;GAGG;AACH,KAAK,UAAU,0BAA0B,CACvC,QAAgB,EAChB,MAAc,EACd,mBAA4B;IAE5B,4DAA4D;IAC5D,MAAM,UAAU,GAAG;QACjB,KAAK,EAAE;YACL,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACvB,GAAG,EAAE;gBACH,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBACjF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBACrF,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBAClF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBACnF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;gBACnF,OAAO,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;aACjF;YACR,QAAQ;YACR,mBAAmB;YACnB,MAAM;YACN,QAAQ,EAAE,QAAQ,MAAM,EAAE;YAC1B,UAAU,EAAE,kBAAkB;YAC9B,KAAK,EAAE,YAAY;SACpB;QACD,gBAAgB,EAAE,EAAE;KACF,CAAC;IAErB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,kEAAc,CAAC,UAAU,EAAE,KAAK,EAAE,UAAuB,EAAE,EAAE;YAC3D,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;YACzE,OAAO,CAAC,UAAU,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,2DAA2D;YAC3D,MAAM,cAAc,GAAgB;gBAClC,KAAK,CAAC,MAAM;oBACV,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBACjD,CAAC;gBACD,KAAK,CAAC,QAAQ;oBACZ,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;gBACnD,CAAC;gBACD,OAAO,EAAE,KAAK,EAAE,GAAW,EAAE,KAAW,EAAE,OAAa,EAAE,EAAE;oBACzD,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC;oBACrD,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;gBACpC,CAAC;gBACD,WAAW,EAAE,KAAK,EAAE,GAAW,EAAE,KAAa,EAAE,OAAa,EAAE,EAAE;oBAC/D,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAC;oBACzD,OAAO,EAAE,YAAY,EAAE,CAAC,EAAE,CAAC;gBAC7B,CAAC;gBACD,UAAU,EAAE,KAAK,EAAE,KAAa,EAAE,EAAE;oBAClC,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;gBAC5D,CAAC;gBACD,KAAK,EAAE,KAAK,IAAI,EAAE;oBAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC/C,CAAC;aACK,CAAC;YACT,OAAO,CAAC,cAAc,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,4BAA4B;AAC5B,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,KAAK,EAAE,GAAW,EAAE,EAAsB,EAAE,EAAE;QAClD,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC;QAC5C,OAAO,MAAM,EAAE,EAAE,CAAC;IACpB,CAAC;IACD,GAAG,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;QACzB,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,EAAE,CAAC,CAAC;QACzC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,GAAG,EAAE,KAAK,EAAE,GAAW,EAAE,KAAU,EAAE,GAAY,EAAE,EAAE;QACnD,OAAO,CAAC,GAAG,CAAC,sBAAsB,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC;IACxD,CAAC;IACD,GAAG,EAAE,KAAK,EAAE,GAAW,EAAE,EAAE;QACzB,OAAO,CAAC,GAAG,CAAC,yBAAyB,GAAG,EAAE,CAAC,CAAC;IAC9C,CAAC;IACD,KAAK,EAAE,KAAK,IAAI,EAAE;QAChB,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC7B,CAAC;CACK,CAAC;AAET,cAAc;AACd,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACjF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACrF,IAAI,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAClF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACnF,KAAK,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IACnF,OAAO,EAAE,CAAC,OAAe,EAAE,GAAG,IAAW,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;CACjF,CAAC;AAET,0BAA0B;AAC1B,MAAM,cAAc,GAAG,IAAI,GAAG,EAAe,CAAC;AAE9C,gCAAgC;AAChC,cAAc,CAAC,GAAG,CAAC,cAAc,EAAE;IACjC,KAAK,EAAE;QACL,WAAW,EAAE,KAAK,EAAE,MAA6B,EAAE,EAAE;YACnD,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;QAChE,CAAC;KACF;CACF,CAAC,CAAC;AAEH,cAAc,CAAC,GAAG,CAAC,iBAAiB,EAAE;IACpC,aAAa,EAAE;QACb,IAAI,EAAE,KAAK,EAAE,MAAW,EAAE,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC;QAC9D,CAAC;KACF;CACF,CAAC,CAAC;AAWH;;GAEG;AACI,MAAM,iBAAiB,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;;IACzF,IAAI;QACF,sEAAsE;QACtE,MAAM,QAAQ,GAAG,UAAG,CAAC,SAAS,0CAAE,QAAQ,KAAI,CAAC,CAAC,CAAC,mBAAmB;QAClE,MAAM,mBAAmB,GAAG,UAAG,CAAC,SAAS,0CAAE,mBAAmB,KAAI,CAAC,CAAC,CAAC,mBAAmB;QACxF,MAAM,MAAM,GAAG,UAAG,CAAC,SAAS,0CAAE,MAAM,KAAI,CAAC,CAAC,CAAC,mBAAmB;QAC9D,MAAM,QAAQ,GAAG,UAAG,CAAC,SAAS,0CAAE,QAAQ,KAAI,QAAQ,MAAM,EAAE,CAAC;QAE7D,oEAAoE;QACpE,MAAM,gBAAgB,GAAG,MAAM,0BAA0B,CAAC,QAAQ,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAEjG,2DAA2D;QAC3D,MAAM,WAAW,GAA+B;YAC9C,UAAU,EAAE,gBAAgB;YAC5B,KAAK,EAAE,SAAS;YAChB,QAAQ;YACR,aAAa,EAAE,KAAK;YACpB,mBAAmB;YACnB,gBAAgB,EAAE,CAAC,mBAAmB,CAAC;YACvC,MAAM;YACN,QAAQ;YACR,GAAG,EAAE,UAAU;YACf,sBAAsB,EAAE,IAAI;YAC5B,oBAAoB,EAAE,KAAK,EAAE,WAAmB,EAAE,EAAE;gBAClD,MAAM,MAAM,GAAG,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;gBAC/C,IAAI,CAAC,MAAM,EAAE;oBACX,MAAM,IAAI,KAAK,CAAC,6BAA6B,WAAW,EAAE,CAAC,CAAC;iBAC7D;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YACD,eAAe,EAAE,CAAC,MAAW,EAAE,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;YACrE,wBAAwB,EAAE,CAAC,WAAmB,EAAE,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;YACpF,0BAA0B,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;YAC3E,uBAAuB,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACrE,kBAAkB,EAAE,CAAC,MAAwB,EAAE,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,GAAG,MAAM,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;YAClG,wBAAwB,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;YAC7E,wBAAwB,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YACvE,4BAA4B,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAC/E,mBAAmB,EAAE,GAAG,EAAE,GAAG,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7D,GAAG,EAAE,EAAS;SACf,CAAC;QAEF,yEAAyE;QACzE,6EAA6E;QAC7E,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC;YAC/B,oBAAoB,EAAE,KAAK,EAAE,gBAAwB,EAAE,WAA4B,EAAE,gBAAuB,EAAE,EAAE;gBAC9G,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,CAAC,CAAC;gBACxG,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YACjE,CAAC;YACD,mBAAmB,EAAE,KAAK,IAAI,EAAE;gBAC9B,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,OAAO,EAAE,cAAc,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,CAAC;YACtE,CAAC;YACD,mBAAmB,EAAE,KAAK,EAAE,MAAW,EAAE,EAAE;gBACzC,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,MAAM,CAAC,CAAC;gBAC1D,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,wBAAwB,EAAE,KAAK,EAAE,mBAA2B,EAAE,KAAY,EAAE,EAAE;gBAC5E,OAAO,CAAC,GAAG,CAAC,yCAAyC,EAAE,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,CAAC;gBACvF,OAAO,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,KAAc,EAAE,YAAY,EAAE,EAAS,EAAE,CAAC,CAAC,CAAC;YAClJ,CAAC;YACD,4BAA4B,EAAE,KAAK,EAAE,MAAc,EAAE,EAAE;gBACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,EAAE,MAAM,CAAC,CAAC;gBACnE,OAAO;oBACL,EAAE,EAAE,MAAM;oBACV,MAAM;oBACN,cAAc,EAAE,MAAM;oBACtB,MAAM,EAAE,GAAY;oBACpB,OAAO,EAAE,CAAC;oBACV,QAAQ,EAAE,QAAQ,MAAM,MAAM;oBAC9B,QAAQ,EAAE,IAAI;oBACd,QAAQ,EAAE,YAAY;oBACtB,GAAG,EAAE,+BAA+B,MAAM,WAAW;iBACtD,CAAC;YACJ,CAAC;YACD,qBAAqB,EAAE,KAAK,EAAE,gBAAwB,EAAE,WAA4B,EAAE,OAAa,EAAE,EAAE;gBACrG,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,OAAO,EAAE,CAAC,CAAC;gBAChG,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,yBAAyB,EAAE,KAAK,EAAE,gBAAwB,EAAE,YAAiC,EAAE,OAAa,EAAE,EAAE;gBAC9G,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;gBACrG,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,8BAA8B,EAAE,KAAK,EAAE,gBAAwB,EAAE,YAAiC,EAAE,OAAa,EAAE,EAAE;gBACnH,OAAO,CAAC,GAAG,CAAC,+CAA+C,EAAE,EAAE,gBAAgB,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC1G,OAAO,CAAC,CAAC;YACX,CAAC;YACD,uBAAuB,EAAE,KAAK,EAAE,gBAAwB,EAAE,WAA4B,EAAE,gBAAuB,EAAE,QAAc,EAAE,EAAE;gBACjI,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,EAAE,gBAAgB,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,CAAC,CAAC;gBACrH,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YACvD,CAAC;YACD,0BAA0B,EAAE,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;gBACnD,OAAO,CAAC,GAAG,CAAC,2CAA2C,EAAE,IAAI,CAAC,CAAC;gBAC/D,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,yBAAyB,EAAE,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;gBAClD,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAC;gBAC9D,OAAO,EAAE,CAAC;YACZ,CAAC;SACF,CAAC,CAAC;QAEH,8BAA8B;QAC9B,MAAM,aAAa,GAAa;YAC9B,GAAG,WAAW;YACd,QAAQ;SACT,CAAC;QAEF,4BAA4B;QAC5B,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC;QAE5B,IAAI,EAAE,CAAC;KACR;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,sCAAsC;SAC9C,CAAC,CAAC;KACJ;AACH,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,UAAU,GAAG,CAAC,GAAY,EAAY,EAAE;IACnD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;QAChB,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;KAChF;IACD,OAAO,GAAG,CAAC,OAAO,CAAC;AACrB,CAAC,CAAC;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,MAAW,EAAE,YAAoB,EAAE,UAA8B,EAAE,EAAE;IAC/F,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC;IAEhC,UAAU,CAAC,KAAK,GAAG,UAAU,GAAG,IAAW;QACzC,2CAA2C;QAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC;QACjD,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE;YACtB,0DAA0D;YAC1D,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACjC;QACD,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;;;;;;;;;;;;;;;;;;;ACvRyC;AAE3C,MAAM,oBAAoB,GAAkB,sBAAsB,CAAC;AACnE,MAAM,MAAM,GAAkB,QAAQ,CAAC;AACvC,MAAM,QAAQ,GAAkB,UAAU,CAAC;AACpC,MAAM,OAAO,GAAkB,SAAS,CAAC;AAChD,MAAM,QAAQ,GAAkB,UAAU,CAAC;AAC3C,MAAM,aAAa,GAAkB,eAAe,CAAC;AAUrD,MAAM,GAAG,GAAG;IACV,CAAC,oBAAoB,CAAC,EAAE,CAAC;IACzB,CAAC,MAAM,CAAC,EAAE,CAAC;IACX,CAAC,QAAQ,CAAC,EAAE,CAAC;IAEb,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IACd,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACb,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;CACpB,CAAC;AAEF,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,oEAAoB,CAC3E,GAAG,CACJ,CAAC;AAEF,iEAAe;IACb,oBAAoB;IACpB,MAAM;IACN,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,aAAa;IAEb,SAAS;IACT,SAAS;IACT,SAAS;IACT,WAAW;CACZ,EAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3C+B;AACuB;AACE;AACN;AAIoB;AAGjE,IAAM,sBAAsB,GAA5B,MAAM,sBAAsB;IAEjC,KAAK,CAAC,qBAAqB,CACzB,OAAiB,EACjB,MAA8B;QAE9B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,mBAAmB,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QACtE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,QAAQ,CAAC;QAExC,+BAA+B;QAC/B,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,WAAW,EAAE,GAAG,MAAM,CAAC;QAE1D,iCAAiC;QACjC,MAAM,OAAO,GAAG,MAAM,wEAAsB,CAAC,UAAU,EAAE;YACvD,GAAG,WAAW;YACd,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,4DAAY,EAAE;YACzB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,4DAAY,EAAE;SAC1B,CAAC,CAAC;QAEH,sCAAsC;QACtC,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;YAC5B,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,QAAQ,CAAC;YAErD,yBAAyB;YACzB,MAAM,IAAI,GAAG,MAAM,qEAAmB,CAAC,UAAU,EAAE;gBACjD,GAAG,UAAU;gBACb,SAAS,EAAE,OAAO,CAAC,EAAE;gBACrB,QAAQ;aACT,CAAC,CAAC;YAEH,iCAAiC;YACjC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,MAAM,QAAQ,CAAC,uBAAuB,CACpC,gFAAsB,EACtB,IAAI,CAAC,EAAE,EACP,WAAW,EACX,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAClC,CAAC;aACH;SACF;QAED,qBAAqB;QACrB,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;QAE1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,OAAiB,EACjB,MAA8B;QAE9B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEjD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,WAAW,EAAE,GAAG,MAAM,CAAC;QAE1D,iCAAiC;QACjC,MAAM,OAAO,GAAG,MAAM,wEAAsB,CAAC,UAAU,EAAE;YACvD,GAAG,WAAW;YACd,QAAQ;YACR,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,4DAAY,EAAE;SAC1B,CAAC,CAAC;QAEH,2DAA2D;QAC3D,KAAK,MAAM,QAAQ,IAAI,KAAK,EAAE;YAC5B,IAAI,QAAQ,CAAC,EAAE,EAAE;gBACf,MAAM,qEAAmB,CAAC,UAAU,EAAE;oBACpC,GAAG,QAAQ;oBACX,QAAQ;iBACT,CAAC,CAAC;aACJ;iBAAM;gBACL,MAAM,qEAAmB,CAAC,UAAU,EAAE;oBACpC,GAAG,QAAQ;oBACX,SAAS,EAAE,OAAO,CAAC,EAAE;oBACrB,QAAQ;iBACT,CAAC,CAAC;aACJ;SACF;QAED,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;QAE1B,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,OAAiB,EACjB,EAAU;QAEV,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAEzC,OAAO,MAAM,wEAAsB,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;IACpE,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,OAAiB,EACjB,MAMC;QAED,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAE3B,MAAM,QAAQ,GAAQ,EAAE,QAAQ,EAAE,CAAC;QACnC,IAAI,MAAM,CAAC,MAAM;YAAE,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QACnD,IAAI,MAAM,CAAC,kBAAkB;YAAE,QAAQ,CAAC,kBAAkB,GAAG,MAAM,CAAC,kBAAkB,CAAC;QACvF,OAAO,MAAM,wEAAsB,CAAC,UAAU,EAAE,QAAQ,EAAE;YACxD,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,CAAC;YACxB,KAAK,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE;SAC1B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,OAAiB,EACjB,EAAU;QAEV,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAEjD,iCAAiC;QACjC,MAAM,wEAAsB,CAAC,UAAU,EAAE;YACvC,EAAE;YACF,QAAQ;YACR,MAAM,EAAE,SAAS;YACjB,SAAS,EAAE,MAAM;YACjB,SAAS,EAAE,4DAAY,EAAE;SAC1B,CAAC,CAAC;QAEH,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;IAC5B,CAAC;CACF;AAvIY,sBAAsB;IADlC,+CAAO,EAAE;GACG,sBAAsB,CAuIlC;AAvIkC;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVF;AAGjC,MAAM,KAAK,GAKF,EAAE,CAAC;AAGL,IAAM,YAAY,GAAlB,MAAM,YAAY;IACrB;IACA,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,MAAe;QACvB,OAAO,EAAC,KAAK,EAAG,OAAO,EAAC,CAAC;IAC7B,CAAC;CACJ;AAPY,YAAY;IADxB,+CAAO,EAAE;;GACG,YAAY,CAOxB;AAPwB;;;;;;;;;;;;;;;;;ACV4B;AAEtC,KAAK,UAAU,aAAa,CACzC,OAAiB;IAEjB,OAAO,+DAAa,CAAC,CAAC,CAAC,MAAM,oEAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChE,CAAC;;;;;;;;;;;ACPD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,QAAQ,QAAQ,EAAE,mBAAO,CAAC,8CAAoB;AAC9C,QAAQ,+CAA+C;AACvD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACvBA;;AAEA;AACA;AACA;;AAEA,QAAQ,QAAQ,EAAE,mBAAO,CAAC,8CAAoB;AAC9C,QAAQ,+CAA+C;AACvD;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACjBA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;;;;;;ACAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACN0B;AACM;AACF;AAE9B,6BAA6B;AAC7B,8CAAM,EAAE,CAAC;AAC4D;AAClC;AACuC;AACoB;AACpB;AACE;AAMvC;AAE9B,MAAM,MAAM,GAAG;IAClB,KAAK,EAAE,GAAG,EAAE;QACR,iEAAY,CAAC,6CAAS,CAAC,CAAC;QAExB,MAAM,GAAG,GAAG,8CAAO,EAAE,CAAC;QAEtB,GAAG,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE,GAAG,EAAE,IAAI;YAC5B,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,yDAAyD;YAC5E,IAAI,EAAE,CAAC;QACX,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,GAAG,CAAC,yDAAkB,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAChD,GAAG,CAAC,GAAG,CAAC,mDAAY,EAAE,CAAC,CAAC;QAExB,yBAAyB;QACzB,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC;QACzC,IAAI,CAAC,SAAS,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;SAC1F;QAED,8DAA8D;QAC9D,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,8EAAsB,CAAC,CAAC;QACxC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,kEAAU,CAAC,CAAC;QAE5B,kEAAkE;QAClE,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,mEAAW,CAAC,CAAC;QAC7B,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,qEAAa,CAAC,CAAC;QAE/B,gEAAgE;QAChE,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,sFAAiB,CAAC,CAAC;QAEnC,wBAAwB;QACxB,qEAAgB,CAAC,GAAG,EAAE;YAClB,WAAW,EAAE,MAAM;YACnB,WAAW,EAAE;gBACT,oFAAe;gBACf,wGAAyB;gBACzB,oFAAe;aAClB;SACJ,CAAC,CAAC;QAEH,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC7B,IAAI,EAAE,CAAC;aACV;iBAAM;gBACH,GAAG,CAAC,GAAG,EAAE,CAAC;aACb;QACL,CAAC,CAAC,CAAC;QAEH,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,GAAG,EAAE;YACrD,sCAAsC;YACtC,OAAO,CAAC,GAAG,CAAC,sDAAsD,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,CAAC;QAClG,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC;CACJ,CAAC;AAEF,MAAM,CAAC,KAAK,EAAE,CAAC", "sources": ["webpack://api-backend/./src/constants/fsCategoriesAreas.ts", "webpack://api-backend/./src/controllers/eContentContent.controller.ts", "webpack://api-backend/./src/controllers/files.controller.ts", "webpack://api-backend/./src/controllers/hello.controller.ts", "webpack://api-backend/./src/entities/EContentClob.ts", "webpack://api-backend/./src/entities/EContentContent.ts", "webpack://api-backend/./src/entities/Tenant.ts", "webpack://api-backend/./src/helpers/token.ts", "webpack://api-backend/./src/middleware/authMiddleware.ts", "webpack://api-backend/./src/middleware/contextMiddleware.ts", "webpack://api-backend/./src/models/TenantStatus.ts", "webpack://api-backend/./src/services/eContentContent.service.ts", "webpack://api-backend/./src/services/hello.service.ts", "webpack://api-backend/./src/services/tenant.service.ts", "webpack://api-backend/./src/models/TenantInstitutionType.js", "webpack://api-backend/./src/models/TenantPlatform.js", "webpack://api-backend/external commonjs \"dotenv\"", "webpack://api-backend/external commonjs \"edana-microservice\"", "webpack://api-backend/external commonjs \"express\"", "webpack://api-backend/external commonjs \"jsonwebtoken\"", "webpack://api-backend/external commonjs \"multer\"", "webpack://api-backend/external commonjs \"reflect-metadata\"", "webpack://api-backend/external commonjs \"routing-controllers\"", "webpack://api-backend/external commonjs \"typedi\"", "webpack://api-backend/webpack/bootstrap", "webpack://api-backend/webpack/runtime/compat get default export", "webpack://api-backend/webpack/runtime/define property getters", "webpack://api-backend/webpack/runtime/hasOwnProperty shorthand", "webpack://api-backend/webpack/runtime/make namespace object", "webpack://api-backend/./src/index.ts"], "sourcesContent": ["// File system category areas for different types of attachments\nexport const E_CONTENT_CONTENT_FILE = 'econtent-content-file';\nexport const E_CONTENT_RESOURCE_FILE = 'econtent-resource-image';\nexport const E_CONTENT_FOLDER_IMAGE = 'econtent-folder-image';\nexport const E_CONTENT_QUESTION_OPTION_FILE = 'econtent-question-option-file';\nexport const E_CONTENT_QUESTION_TEXTAREA_FILE = 'econtent-content-textarea-file';\n\n// Other common areas\nexport const ACCOUNT_PERSONAL_DRIVE_FILE = 'account-personal-drive-file';\nexport const IMPORT_SESSION_AREA = 'import-session-area';\n", "import {\n  Body,\n  Get,\n  Post,\n  Put,\n  Delete,\n  JsonController,\n  Param,\n  QueryParams,\n  Res,\n  Req,\n  UseBefore,\n  UploadedFiles\n} from 'routing-controllers';\nimport { Service } from 'typedi';\nimport { Request, Response } from 'express';\nimport multer from 'multer';\nimport { EContentContentService } from '../services/eContentContent.service';\nimport { IEContentContentCreate, IEContentContentUpdate } from '../interfaces/IEContentContent';\nimport { IContext } from 'edana-microservice';\nimport { IAttachFileInfo } from '../interfaces/IAttachFileInfo';\nimport { getContext } from '../middleware/contextMiddleware';\n\n// Configure multer for file uploads\nconst upload = multer({\n  storage: multer.memoryStorage(),\n  limits: {\n    fileSize: 10 * 1024 * 1024, // 10MB limit\n  },\n});\n\n// Middleware for file uploads\nconst fileUploadMiddleware = upload.array('files', 10);\n\n@JsonController('/econtent')\n@Service()\nexport class EContentContentController {\n  constructor(private eContentContentService: EContentContentService) {}\n\n\n\n  @Get('/contents')\n  async getEContentContents(\n    @QueryParams() query: {\n      itemId?: number;\n      resourceLanguageId?: number;\n      first?: number;\n      count?: number;\n      searchQuery?: string;\n    },\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      const contents = await this.eContentContentService.getEContentContents(context, query);\n      return response.json({\n        success: true,\n        data: contents,\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Get('/contents/:id')\n  async getEContentContent(\n    @Param('id') id: number,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      const content = await this.eContentContentService.getEContentContent(context, id);\n      return response.json({\n        success: true,\n        data: content,\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Post('/contents/simple')\n  async createEContentContentSimple(\n    @Body() body: any,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      const content = await this.eContentContentService.createEContentContent(context, body);\n\n      return response.status(201).json({\n        success: true,\n        data: content,\n        message: 'EContentContent created successfully (simple)',\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Post('/contents')\n  async createEContentContent(\n    @Body() body: any,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n\n      // Parse the data if it comes from multipart form\n      let contentData = body;\n      if (typeof body.data === 'string') {\n        contentData = JSON.parse(body.data);\n      }\n\n      // Handle file uploads from request.files if available\n      const files = (request as any).files || [];\n      const attachments: IAttachFileInfo[] = files.map((file: any) => ({\n        fileName: file.originalname || file.name,\n        fileSize: file.size,\n        mimeType: file.mimetype || file.type,\n        content: file.buffer || file.data,\n      }));\n\n      // Add attachments to the first CLOB if files were uploaded\n      if (attachments.length > 0 && contentData.clobs && contentData.clobs.length > 0) {\n        contentData.clobs[0].attachments = attachments;\n      }\n\n      const content = await this.eContentContentService.createEContentContent(context, contentData);\n\n      return response.status(201).json({\n        success: true,\n        data: content,\n        message: 'EContentContent created successfully',\n        filesUploaded: attachments.length,\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Put('/contents/:id')\n  @UseBefore(fileUploadMiddleware)\n  async updateEContentContent(\n    @Param('id') id: number,\n    @Body() body: Omit<IEContentContentUpdate, 'id'>,\n    @UploadedFiles('files') files: Express.Multer.File[],\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      \n      // Process uploaded files\n      const attachments: IAttachFileInfo[] = files ? files.map(file => ({\n        fileName: file.originalname,\n        fileSize: file.size,\n        mimeType: file.mimetype,\n        content: file.buffer,\n      })) : [];\n\n      // Add attachments to the first CLOB if files were uploaded\n      if (attachments.length > 0 && body.clobs && body.clobs.length > 0) {\n        body.clobs[0].attachments = attachments;\n      }\n\n      const updateData: IEContentContentUpdate = { ...body, id };\n      const content = await this.eContentContentService.updateEContentContent(context, updateData);\n      \n      return response.json({\n        success: true,\n        data: content,\n        message: 'EContentContent updated successfully',\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Delete('/contents/:id')\n  async deleteEContentContent(\n    @Param('id') id: number,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      await this.eContentContentService.deleteEContentContent(context, id);\n\n      return response.json({\n        success: true,\n        message: 'EContentContent deleted successfully',\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n}\n", "import { Get, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Req } from 'routing-controllers';\nimport { Service } from 'typedi';\nimport { Request, Response } from 'express';\nimport { getContext } from '../middleware/contextMiddleware';\n\n@JsonController('/files')\n@Service()\nexport class FilesController {\n  \n\n\n  @Get('/:fileId')\n  async getFile(\n    @Param('fileId') fileId: number,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      const fsClient = await context.fsClient;\n      \n      // Get file metadata\n      const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);\n      \n      if (!fileInfo) {\n        return response.status(404).json({\n          success: false,\n          error: 'File not found',\n        });\n      }\n\n      // For this mock implementation, we'll return file metadata\n      // In a real implementation, you would stream the actual file content\n      return response.json({\n        success: true,\n        data: {\n          fileId: fileInfo.fileId,\n          fileName: fileInfo.fileName,\n          fileSize: fileInfo.fileSize,\n          mimeType: fileInfo.mimeType,\n          url: fileInfo.url,\n        },\n      });\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n\n  @Get('/:fileId/download')\n  async downloadFile(\n    @Param('fileId') fileId: number,\n    @Req() request: Request,\n    @Res() response: Response\n  ) {\n    try {\n      const context = getContext(request);\n      const fsClient = await context.fsClient;\n      \n      const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);\n      \n      if (!fileInfo) {\n        return response.status(404).json({\n          success: false,\n          error: 'File not found',\n        });\n      }\n\n      // Set appropriate headers for file download\n      response.setHeader('Content-Type', fileInfo.mimeType);\n      response.setHeader('Content-Disposition', `attachment; filename=\"${fileInfo.fileName}\"`);\n      response.setHeader('Content-Length', fileInfo.fileSize);\n\n      // For this mock implementation, return a simple text response\n      // In a real implementation, you would stream the actual file\n      return response.send(`Mock file content for ${fileInfo.fileName}`);\n    } catch (error) {\n      return response.status(500).json({\n        success: false,\n        error: error.message,\n      });\n    }\n  }\n}\n", "import { Body, BodyParam, Get, JsonController, Param, Post, QueryParams, Res } from 'routing-controllers';\nimport { HelloService } from '@webaverse/services/hello.service';\nimport { Service } from 'typedi';\n\n@JsonController('/hello')\n@Service()\nexport class HelloController {\n    constructor(private helloService:HelloService) {\n    }\n\n    @Get('')\n    async getRequest(@QueryParams() query, @Res() response: any) {\n        return response.send(await this.helloService.hello());\n    }\n\n    @Post('')\n    async postReqeust(@QueryParams() query, @Res() response: any) {\n        return response.send(await this.helloService.hello());\n    }\n\n}\n", "import { Entity, validators } from 'edana-microservice';\nimport { IEContentClob } from '../interfaces/IEContentClob';\n\nconst { tenantValidator } = validators;\n\nexport default new Entity<IEContentClob>(\n  'EContentClob',\n  {\n    tenantId: 'number',\n    resourceId: 'number',\n    itemId: 'number',\n    contentId: 'number',\n    languageId: 'number',\n    resourceAttributeId: 'number',\n    attributeId: 'number',\n    content: 'clob' as any,\n    groupSequence: 'number',\n    contentClobTypeId: 'number',\n    resourceCategoryId: 'number',\n  },\n  {},\n  { tableName: 'ECN_CONTENT_CLOB' },\n  {\n    validators: [\n      {\n        actions: ['update'],\n        validators: [\n          {\n            validate: tenantValidator,\n            params: ({ id, tenantId }) => ({ id, tenantId }),\n            errorMessage: 'tenantAccessError',\n          },\n        ],\n      },\n    ],\n  },\n);\n", "import { Entity, validators, DateTime, TStatus } from 'edana-microservice';\nimport { IEContentContent } from '../interfaces/IEContentContent';\n\nconst { tenantValidator } = validators;\n\nexport default new Entity<IEContentContent>(\n  'EContentContent',\n  {\n    tenantId: 'number',\n    itemId: 'number',\n    resourceLanguageId: 'number',\n    description: 'string',\n    createdBy: 'string',\n    createdAt: 'datetime',\n    updatedBy: 'number',\n    updatedAt: 'datetime',\n    sequence: 'number',\n    status: 'number',\n    headingSearchTerm: 'string',\n  },\n  {},\n  { tableName: 'ECN_CONTENT' },\n  {\n    validators: [\n      {\n        actions: ['update'],\n        validators: [\n          {\n            validate: tenantValidator,\n            params: ({ id, tenantId }) => ({ id, tenantId }),\n            errorMessage: 'tenantAccessError',\n          },\n        ],\n      },\n    ],\n  },\n);\n", "import { DateTime, Entity, validators } from \"edana-microservice\";\n\nimport TenantInstitutionType from \"../models/TenantInstitutionType\";\n\nimport TenantPlatform from \"../models/TenantPlatform\";\n\nimport TenantStatus from \"../models/TenantStatus\";\n\nconst Tenant = new Entity<ITenant>(\n  \"Tenant\",\n  {\n    domain: \"string\",\n    clientAbbreviation: \"string\",\n    siteLink: \"string\",\n    licenseKey: \"string\",\n    siteName: \"string\",\n    logoFileId: \"number\",\n    loginBgImgId: \"number\",\n    appLoginBgImgId: \"number\",\n    loginPageMessage: \"string\",\n    createdAt: \"date\",\n    status: {\n      preset: \"number\",\n      serialize: TenantStatus.serialize,\n      deserialize: TenantStatus.deserialize,\n      canUpdate: true,\n    },\n    platform: {\n      columnName: \"PLATFORM_ID\",\n      preset: \"number\",\n      serialize: TenantPlatform.serialize,\n      deserialize: TenantPlatform.deserialize,\n      canUpdate: true,\n    },\n    institutionType: {\n      columnName: \"INSTITUTION_TYPE_ID\",\n      preset: \"number\",\n      serialize: TenantInstitutionType.serialize,\n      deserialize: TenantInstitutionType.deserialize,\n      canUpdate: true,\n    },\n    countryId: \"number\",\n    licenseName: \"string\",\n  },\n  {},\n  {\n    tableName: \"C_TENANT\",\n    cache: {\n      key: \"Tenant\",\n      list: true,\n      loader: async (connection) => await Tenant.loadAll(connection),\n    },\n  },\n  {}\n);\n\nexport default Tenant;\n\nexport interface ITenant {\n  __type: \"Tenant\";\n  id: number;\n  domain?: string;\n  siteName: string;\n  clientAbbreviation: string;\n  siteLink: string;\n  licenseKey: string;\n  licenseName?: string;\n  logoFileId?: number;\n  loginBgImgId?: number;\n  appLoginBgImgId?: number;\n  createdAt: DateTime | string;\n  status: string;\n  platform?: string;\n  institutionType: string;\n  countryId: number;\n  loginPageMessage?: string;\n}\n", "import jwt from \"jsonwebtoken\";\nimport { Request } from \"express\";\n\nexport interface TokenPayload {\n  userId: number;\n  organisationGroupId: number;\n  userName: string;\n  accessHash: string;\n  tenantId?: number;\n}\n\n/**\n * Extract JWT token from Express request with proper audience validation\n * Production-ready version with security checks\n */\nexport function extractToken(\n  req: Request,\n  token: string\n): Promise<TokenPayload | null> {\n  return new Promise((resolve, reject) => {\n    const secret = process.env.JWT_SECRET;\n    if (!secret) {\n      reject(new Error(\"JWT_SECRET environment variable is required\"));\n      return;\n    }\n\n    // Get audience from request domain\n    const domain = getDomain(req);\n\n    // For Bearer tokens, use the token directly\n    // For cookie tokens, extract from cookie string\n    let jwtToken = token;\n    if (token && token.includes('=')) {\n      // This looks like a cookie string, extract the JWT\n      const authKey = `Authorization_${domain}`;\n      jwtToken = extractJwt(token, authKey);\n    }\n\n    if (!jwtToken) {\n      resolve(null);\n      return;\n    }\n\n    jwt.verify(jwtToken, secret, { audience: domain }, (err, payload) => {\n      if (err) {\n        if (\n          err instanceof jwt.TokenExpiredError ||\n          err.message.includes(\"audience invalid\")\n        ) {\n          resolve(null);\n        } else {\n          reject(err);\n        }\n      } else {\n        resolve(payload as TokenPayload);\n      }\n    });\n  });\n}\n\nfunction isDev(): boolean {\n  return process.env.ENV === \"development\";\n}\n\nexport function getDomain(req: Request): string {\n  return isDev()\n    ? process.env.AUDIENCE_TEMPLATE || req.get(\"host\") || \"localhost\"\n    : req.get(\"host\") || \"localhost\";\n}\nfunction extractJwt(\n  cookieString: string,\n  key: string,\n): string | null {\n  if (!cookieString) return null;\n\n  // Split cookies by ;\n  const cookies = cookieString.split(\";\").map((c) => c.trim());\n\n  // Find the one that starts with the given key\n  const target = cookies.find((c) => c.startsWith(key + \"=\"));\n  if (!target) return null;\n\n  // Return the value after =\n  return target.split(\"=\")[1] || null;\n}\n\n/**\n * Extract token from Authorization header or signed cookies\n * Production-ready version with proper cookie handling\n */\nexport function getTokenFromRequest(req: Request): string | undefined {\n  // Try Authorization header first (Bearer token)\n  const authHeader = req.headers.authorization;\n  if (authHeader && authHeader.startsWith(\"Bearer \")) {\n    return authHeader.substring(7);\n  }\n\n  // Try signed cookies (production approach)\n  if (req.signedCookies) {\n    const domain = getDomain(req);\n    const authKey = `Authorization_${domain}`;\n\n    if (req.signedCookies[authKey]) {\n      return req.signedCookies[authKey];\n    }\n  }\n\n  // Fallback to regular cookies for development\n  const cookieToken = req.headers.cookie\n    ?.split(\";\")\n    .find((c) => c.trim().startsWith(\"auth_token=\"))\n    ?.split(\"=\")[1];\n\n  return cookieToken;\n}\n", "import { Request, Response, NextFunction } from \"express\";\nimport {\n  extractToken,\n  getDomain,\n  getTokenFromRequest,\n  TokenPayload,\n} from \"../helpers/token\";\n// import { find, get } from \"lodash\"; // Not needed with simplified tenant lookup\nimport getAllTenants from \"../services/tenant.service\";\nimport { withConnection, IConnection, ICtx, IContext } from \"edana-microservice\";\n\n/**\n * Create a temporary context for tenant resolution\n * This allows us to query the database before the main context is created\n */\nasync function createTemporaryContext(): Promise<IContext> {\n  return new Promise((resolve, reject) => {\n    // Create a minimal Koa context for withConnection\n    const mockKoaCtx = {\n      state: {\n        id: `tenant_lookup_${Date.now()}`,\n        log: {\n          info: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP INFO:', message, ...args),\n          error: (message: string, ...args: any[]) => console.error('TENANT_LOOKUP ERROR:', message, ...args),\n          warn: (message: string, ...args: any[]) => console.warn('TENANT_LOOKUP WARN:', message, ...args),\n          debug: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP DEBUG:', message, ...args),\n          silly: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP SILLY:', message, ...args),\n          verbose: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP VERBOSE:', message, ...args),\n        } as any,\n        tenantId: 1, // Use a default tenant for the lookup query\n        organisationGroupId: 1,\n        userId: 1,\n        userName: 'system_tenant_lookup',\n        accessHash: 'system_lookup',\n        token: 'system_lookup',\n      },\n      customProperties: {},\n    } as unknown as ICtx;\n\n    withConnection(mockKoaCtx, async (connection: IConnection) => {\n      console.log('Temporary context created for tenant lookup');\n\n      // Create a simple cache for tenant lookup\n      const tenantCache = new Map<string, any>();\n\n      // Create a minimal context with just what we need for tenant lookup\n      const tempContext: IContext = {\n        connection,\n        cache: {\n          wrap: async (key: string, loader: () => Promise<any>) => {\n            if (tenantCache.has(key)) {\n              console.log(`Tenant cache hit: ${key}`);\n              return tenantCache.get(key);\n            }\n            console.log(`Tenant cache miss: ${key}`);\n            const result = await loader();\n            tenantCache.set(key, result);\n            return result;\n          },\n          invalidate: async (key: string) => {\n            tenantCache.delete(key);\n          }\n        } as any,\n        tenantId: 1,\n        userId: 1,\n        userName: 'system_tenant_lookup',\n        log: mockKoaCtx.state.log,\n      } as any;\n\n      resolve(tempContext);\n    }).catch((error) => {\n      console.error('Failed to create temporary context for tenant lookup:', error);\n      reject(error);\n    });\n  });\n}\n\n// Extend Express Request to include auth state\ndeclare global {\n  namespace Express {\n    interface Request {\n      authState?: {\n        token?: string;\n        userId?: number;\n        userName?: string;\n        accessHash?: string;\n        organisationGroupId?: number;\n        tenantId?: number;\n        isSubstitution?: boolean;\n      };\n    }\n  }\n}\n\n/**\n * Express middleware equivalent to withCookieUserToken from edana2_api\n * Extracts JWT token from signed cookies\n */\nexport const withCookieUserToken = async (\n  req: Request,\n  res: Response,\n  next: NextFunction\n): Promise<void> => {\n  req.authState = req.authState || {};\n\n  // Extract token from signed cookies (production-ready)\n  const cookieSecret = process.env.COOKIE_SECRET;\n  if (!cookieSecret) {\n    res.status(500).json({\n      success: false,\n      error: \"Server configuration error: COOKIE_SECRET not set\",\n    });\n    return;\n  }\n\n  // Get domain-specific cookie key\n  const domain = getDomain(req);\n  const authKey = `Authorization_${domain}`;\n\n  // Extract token from signed cookie\n  if (req.signedCookies && req.signedCookies[authKey]) {\n    req.authState.token = req.signedCookies[authKey];\n  }\n\n  next();\n};\n\n/**\n * Express middleware equivalent to withSubstitutionAuthentication from edana2_api\n * Checks for substitution authentication via signed cookies\n */\nexport const withSubstitutionAuthentication = async (\n  req: Request,\n  res: Response,\n  next: NextFunction\n): Promise<void> => {\n  req.authState = req.authState || {};\n\n  if (typeof req.authState.isSubstitution === \"undefined\") {\n    const domain = getDomain(req);\n    const originAuthKey = `OriginalAuthorization_${domain}`;\n\n    req.authState.isSubstitution = !!(\n      req.signedCookies && req.signedCookies[originAuthKey]\n    );\n  }\n\n  next();\n};\n\n/**\n * Express middleware equivalent to withUserAuthentication from edana2_api\n * Extracts and validates JWT token, populates user information\n */\nexport const withUserAuthentication = async (\n  req: Request,\n  res: Response,\n  next: NextFunction\n): Promise<void> => {\n  req.authState = req.authState || {};\n\n  // Extract token from request (Authorization header or cookies)\n  const token = req.authState.token || getTokenFromRequest(req);\n  if (token) {\n    req.authState.token = token;\n\n    try {\n      const payload = await extractToken(req, token);\n      if (payload && payload.userId) {\n        req.authState.userId = payload.userId;\n        req.authState.userName = payload.userName;\n        req.authState.accessHash = payload.accessHash;\n        req.authState.organisationGroupId = payload.organisationGroupId;\n        if (payload.tenantId) {\n          req.authState.tenantId = payload.tenantId;\n        }\n      }\n    } catch (e) {\n      console.warn(\"Invalid JWT token:\", e);\n      res.status(403).json({\n        success: false,\n        error: \"Invalid authentication token\",\n      });\n      return;\n    }\n  }\n\n  next();\n};\n\n\n/**\n * Express middleware equivalent to withTenant from edana2_api\n * Resolves tenant information from domain using database/cache lookup\n */\nexport const withTenant = async (\n  req: Request,\n  _res: Response,\n  next: NextFunction\n): Promise<void> => {\n  req.authState = req.authState || {};\n\n  try {\n    // Get domain from request\n    const domain = getDomain(req);\n    console.log('Resolving tenant for domain:', domain);\n\n    // Create temporary context for tenant lookup\n    const tempContext = await createTemporaryContext();\n\n    // Fetch all tenants from database/cache\n    const tenants = await getAllTenants(tempContext);\n    console.log(`Loaded ${tenants.length} tenants for domain matching`);\n\n    // Find tenant by domain\n    const tenant = tenants.find((t) => t.domain === domain);\n\n    if (tenant && tenant.status === 'ACTIVE') { // Active tenant\n      req.authState.tenantId = tenant.id;\n      console.log(`Found active tenant: ${tenant.siteName} (ID: ${tenant.id}) for domain: ${domain}`);\n    } else {\n      // Fallback to JWT token tenant or default\n      const fallbackTenantId = req.authState.tenantId || 2;\n      req.authState.tenantId = fallbackTenantId;\n      console.log(`No active tenant found for domain: ${domain}, using fallback tenant ID: ${fallbackTenantId}`);\n    }\n\n  } catch (error) {\n    console.error('Error in tenant resolution:', error);\n\n    // Error fallback - use JWT token tenant or default\n    const fallbackTenantId = req.authState.tenantId || 2;\n    req.authState.tenantId = fallbackTenantId;\n    console.log(`Error fallback: using tenant ID: ${fallbackTenantId}`);\n  }\n\n  next();\n};\n\n/**\n * Middleware to ensure user is authenticated\n */\nexport const requireAuth = (\n  req: Request,\n  res: Response,\n  next: NextFunction\n): void => {\n  if (!req.authState?.userId) {\n    res.status(401).json({ error: \"Authentication required\" });\n    return;\n  }\n  next();\n};\n\n/**\n * Middleware to ensure tenant is resolved\n */\nexport const requireTenant = (\n  req: Request,\n  res: Response,\n  next: NextFunction\n): void => {\n  if (!req.authState?.tenantId) {\n    res.status(403).json({ error: \"Invalid tenant\" });\n    return;\n  }\n  next();\n};\n", "import { Request, Response, NextFunction } from 'express';\nimport { IContext, IConnection, withConnection, ICtx } from 'edana-microservice';\nimport { extractToken } from '../helpers/token';\n\n/**\n * Create a real Oracle database connection using the edana-microservice framework\n * This uses the withConnection function which handles connection pooling automatically\n */\nasync function createRealOracleConnection(\n  tenantId: number,\n  userId: number,\n  organisationGroupId?: number\n): Promise<IConnection> {\n  // Create a mock Koa context for the withConnection function\n  const mockKoaCtx = {\n    state: {\n      id: `req_${Date.now()}`,\n      log: {\n        info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),\n        error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),\n        warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),\n        debug: (message: string, ...args: any[]) => console.log('DEBUG:', message, ...args),\n        silly: (message: string, ...args: any[]) => console.log('SILLY:', message, ...args),\n        verbose: (message: string, ...args: any[]) => console.log('VERBOSE:', message, ...args),\n      } as any,\n      tenantId,\n      organisationGroupId,\n      userId,\n      userName: `user_${userId}`,\n      accessHash: 'mock_access_hash',\n      token: 'mock_token',\n    },\n    customProperties: {},\n  } as unknown as ICtx;\n\n  return new Promise((resolve, reject) => {\n    withConnection(mockKoaCtx, async (connection: IConnection) => {\n      console.log('Real Oracle connection established via edana-microservice');\n      resolve(connection);\n    }).catch((error) => {\n      console.error('Failed to create real Oracle connection:', error);\n      // Fallback to a mock connection if Oracle connection fails\n      const mockConnection: IConnection = {\n        async commit() {\n          console.log('FALLBACK: Transaction committed');\n        },\n        async rollback() {\n          console.log('FALLBACK: Transaction rolled back');\n        },\n        execute: async (sql: string, binds?: any, options?: any) => {\n          console.log('FALLBACK: Mock database execute:', sql);\n          return { rows: [], outBinds: {} };\n        },\n        executeMany: async (sql: string, binds?: any[], options?: any) => {\n          console.log('FALLBACK: Mock database executeMany:', sql);\n          return { rowsAffected: 0 };\n        },\n        executeAll: async (query: string) => {\n          console.log('FALLBACK: Mock database executeAll:', query);\n        },\n        close: async () => {\n          console.log('FALLBACK: Mock database close');\n        },\n      } as any;\n      resolve(mockConnection);\n    });\n  });\n}\n\n// Mock cache implementation\nconst mockCache = {\n  wrap: async (key: string, fn: () => Promise<any>) => {\n    console.log(`Cache lookup for key: ${key}`);\n    return await fn();\n  },\n  get: async (key: string) => {\n    console.log(`Cache get for key: ${key}`);\n    return null;\n  },\n  set: async (key: string, value: any, ttl?: number) => {\n    console.log(`Cache set for key: ${key}, TTL: ${ttl}`);\n  },\n  del: async (key: string) => {\n    console.log(`Cache delete for key: ${key}`);\n  },\n  clear: async () => {\n    console.log('Cache clear');\n  },\n} as any;\n\n// Mock logger\nconst mockLogger = {\n  info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),\n  error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),\n  warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),\n  debug: (message: string, ...args: any[]) => console.log('DEBUG:', message, ...args),\n  silly: (message: string, ...args: any[]) => console.log('SILLY:', message, ...args),\n  verbose: (message: string, ...args: any[]) => console.log('VERBOSE:', message, ...args),\n} as any;\n\n// Service client registry\nconst serviceClients = new Map<string, any>();\n\n// Register mock service clients\nserviceClients.set('edana_api_fs', {\n  files: {\n    deleteFiles: async (params: { fileIds: number[] }) => {\n      console.log('Service client: deleting files', params.fileIds);\n    },\n  },\n});\n\nserviceClients.set('edana2_api_notf', {\n  notifications: {\n    send: async (params: any) => {\n      console.log('Service client: sending notification', params);\n    },\n  },\n});\n\n// Enhanced context interface for Express requests\ndeclare global {\n  namespace Express {\n    interface Request {\n      context?: IContext;\n    }\n  }\n}\n\n/**\n * Middleware to create and attach microservice context to requests\n */\nexport const contextMiddleware = async (req: Request, res: Response, next: NextFunction) => {\n  try {\n    // Extract tenant and user information from auth middleware or headers\n    const tenantId = req.authState?.tenantId || 2; // Default fallback\n    const organisationGroupId = req.authState?.organisationGroupId || 1; // Default fallback\n    const userId = req.authState?.userId || 1; // Default fallback\n    const userName = req.authState?.userName || `user_${userId}`;\n\n    // Create a real Oracle database connection using edana-microservice\n    const oracleConnection = await createRealOracleConnection(tenantId, userId, organisationGroupId);\n\n    // Create base context matching the real IContext interface\n    const baseContext: Omit<IContext, 'fsClient'> = {\n      connection: oracleConnection,\n      cache: mockCache,\n      tenantId,\n      isSuperTenant: false,\n      organisationGroupId,\n      organisationsIds: [organisationGroupId],\n      userId,\n      userName,\n      log: mockLogger,\n      redisConnectionFactory: null,\n      requireServiceClient: async (serviceName: string) => {\n        const client = serviceClients.get(serviceName);\n        if (!client) {\n          throw new Error(`Service client not found: ${serviceName}`);\n        }\n        return client;\n      },\n      raiseHumanError: (errors: any) => { throw new Error('Human error'); },\n      raiseInvalidRequestError: (description: string) => { throw new Error(description); },\n      raiseNotAuthenticatedError: () => { throw new Error('Not authenticated'); },\n      raiseNotAuthorizedError: () => { throw new Error('Not authorized'); },\n      raiseNotFoundError: (entity: { name: string }) => { throw new Error(`${entity.name} not found`); },\n      raiseInternalServerError: () => { throw new Error('Internal server error'); },\n      raiseNotImplementedError: () => { throw new Error('Not implemented'); },\n      raiseServiceUnavailableError: () => { throw new Error('Service unavailable'); },\n      raiseNotUniqueError: () => { throw new Error('Not unique'); },\n      ctx: {} as any,\n    };\n\n    // Create a mock fsClient since the real withFsClient is designed for Koa\n    // In a real implementation, you would integrate with the actual file service\n    const fsClient = Promise.resolve({\n      addEntityAttachments: async (fileCategoryArea: string, referenceId: number | string, attachmentInputs: any[]) => {\n        console.log('Mock fsClient: addEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs });\n        return attachmentInputs.map((_, index) => ({ id: index + 1 }));\n      },\n      getFileCategoryInfo: async () => {\n        console.log('Mock fsClient: getFileCategoryInfo');\n        return { fileCategories: [], categoryArea: [], categoryGroups: [] };\n      },\n      getStorageInstances: async (params: any) => {\n        console.log('Mock fsClient: getStorageInstances', params);\n        return [];\n      },\n      initiateAttachmentUpload: async (fileCategoryAreaKey: string, files: any[]) => {\n        console.log('Mock fsClient: initiateAttachmentUpload', { fileCategoryAreaKey, files });\n        return files.map(() => ({ ok: true, uploadToken: 'mock-token', uploadUrl: 'mock-url', uploadTarget: 'app' as const, uploadConfig: {} as any }));\n      },\n      loadEntityAttachmentByFileId: async (fileId: number) => {\n        console.log('Mock fsClient: loadEntityAttachmentByFileId', fileId);\n        return {\n          id: fileId,\n          fileId,\n          fileMetadataId: fileId,\n          status: 'A' as const,\n          version: 1,\n          fileName: `file_${fileId}.txt`,\n          fileSize: 1024,\n          mimeType: 'text/plain',\n          url: `http://localhost:3001/files/${fileId}/download`,\n        };\n      },\n      loadEntityAttachments: async (fileCategoryArea: string, referenceId: string | number, options?: any) => {\n        console.log('Mock fsClient: loadEntityAttachments', { fileCategoryArea, referenceId, options });\n        return [];\n      },\n      bulkLoadEntityAttachments: async (fileCategoryArea: string, referenceIds: string[] | number[], options?: any) => {\n        console.log('Mock fsClient: bulkLoadEntityAttachments', { fileCategoryArea, referenceIds, options });\n        return [];\n      },\n      bulkLoadEntityAttachmentsCount: async (fileCategoryArea: string, referenceIds: string[] | number[], options?: any) => {\n        console.log('Mock fsClient: bulkLoadEntityAttachmentsCount', { fileCategoryArea, referenceIds, options });\n        return 0;\n      },\n      updateEntityAttachments: async (fileCategoryArea: string, referenceId: number | string, attachmentInputs: any[], metaInfo?: any) => {\n        console.log('Mock fsClient: updateEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs, metaInfo });\n        return attachmentInputs.map((_, index) => index + 1);\n      },\n      resolveBatchEntityFileSize: async (...args: any[]) => {\n        console.log('Mock fsClient: resolveBatchEntityFileSize', args);\n        return {};\n      },\n      resolveBatchEntityFileUrl: async (...args: any[]) => {\n        console.log('Mock fsClient: resolveBatchEntityFileUrl', args);\n        return {};\n      },\n    });\n\n    // Create the complete context\n    const contextWithFs: IContext = {\n      ...baseContext,\n      fsClient,\n    };\n\n    // Attach context to request\n    req.context = contextWithFs;\n\n    next();\n  } catch (error) {\n    console.error('Context middleware error:', error);\n    res.status(500).json({\n      success: false,\n      error: 'Failed to initialize request context',\n    });\n  }\n};\n\n/**\n * Helper function to get context from request\n */\nexport const getContext = (req: Request): IContext => {\n  if (!req.context) {\n    throw new Error('Context not available. Ensure contextMiddleware is applied.');\n  }\n  return req.context;\n};\n\n/**\n * Decorator to inject context into controller methods\n */\nexport const WithContext = (target: any, propertyName: string, descriptor: PropertyDescriptor) => {\n  const method = descriptor.value;\n\n  descriptor.value = function (...args: any[]) {\n    // Find the request object in the arguments\n    const req = args.find(arg => arg && arg.context);\n    if (req && req.context) {\n      // Replace the mock context creation with the real context\n      return method.apply(this, args);\n    }\n    return method.apply(this, args);\n  };\n\n  return descriptor;\n};\n", "import { utils } from 'edana-microservice';\n\nconst CREATION_IN_PROGRESS: TTenantStatus = 'CREATION_IN_PROGRESS';\nconst ACTIVE: TTenantStatus = 'ACTIVE';\nconst INACTIVE: TTenantStatus = 'INACTIVE';\nexport const DELETED: TTenantStatus = 'DELETED';\nconst REMOVING: TTenantStatus = 'REMOVING';\nconst DEPLOY_FAILED: TTenantStatus = 'DEPLOY_FAILED';\n\nexport type TTenantStatus =\n  | 'CREATION_IN_PROGRESS'\n  | 'ACTIVE'\n  | 'INACTIVE'\n  | 'DELETED'\n  | 'REMOVING'\n  | 'DEPLOY_FAILED';\n\nconst Ids = {\n  [CREATION_IN_PROGRESS]: 0,\n  [ACTIVE]: 1,\n  [REMOVING]: 2,\n\n  [INACTIVE]: -1,\n  [DELETED]: -2,\n  [DEPLOY_FAILED]: -3,\n};\n\nconst { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(\n  Ids,\n);\n\nexport default {\n  CREATION_IN_PROGRESS,\n  ACTIVE,\n  INACTIVE,\n  DELETED,\n  REMOVING,\n  DEPLOY_FAILED,\n\n  valueById,\n  idByValue,\n  serialize,\n  deserialize,\n};\n", "import { Service } from 'typedi';\nimport { IContext, DateTime } from 'edana-microservice';\nimport EContentContent from '../entities/EContentContent';\nimport EContentClob from '../entities/EContentClob';\nimport { IEContentContent, IEContentContentCreate, IEContentContentUpdate } from '../interfaces/IEContentContent';\nimport { IEContentClob, IEContentClobCreate } from '../interfaces/IEContentClob';\nimport { IAttachFileInfo } from '../interfaces/IAttachFileInfo';\nimport { E_CONTENT_CONTENT_FILE } from '../constants/fsCategoriesAreas';\n\n@Service()\nexport class EContentContentService {\n  \n  async createEContentContent(\n    context: IContext,\n    params: IEContentContentCreate\n  ): Promise<IEContentContent> {\n    const { connection, tenantId, organisationGroupId, userId } = context;\n    const fsClient = await context.fsClient;\n    \n    // Extract clobs and other data\n    const { clobs, questionOptions, ...contentData } = params;\n    \n    // Create the main content entity\n    const content = await EContentContent.create(connection, {\n      ...contentData,\n      tenantId,\n      createdBy: userId,\n      createdAt: DateTime.utc(),\n      updatedBy: userId,\n      updatedAt: DateTime.utc(),\n    });\n\n    // Process CLOBs with file attachments\n    for (const clobData of clobs) {\n      const { attachments = [], ...clobFields } = clobData;\n      \n      // Create the CLOB entity\n      const clob = await EContentClob.create(connection, {\n        ...clobFields,\n        contentId: content.id,\n        tenantId,\n      });\n\n      // Handle file attachments if any\n      if (attachments.length > 0) {\n        await fsClient.updateEntityAttachments(\n          E_CONTENT_CONTENT_FILE,\n          clob.id,\n          attachments,\n          { tenantId, organisationGroupId }\n        );\n      }\n    }\n\n    // Commit transaction\n    await connection.commit();\n    \n    return content;\n  }\n\n  async updateEContentContent(\n    context: IContext,\n    params: IEContentContentUpdate\n  ): Promise<IEContentContent> {\n    const { connection, tenantId, userId } = context;\n    \n    const { clobs, questionOptions, ...contentData } = params;\n    \n    // Update the main content entity\n    const content = await EContentContent.update(connection, {\n      ...contentData,\n      tenantId,\n      updatedBy: userId,\n      updatedAt: DateTime.utc(),\n    });\n\n    // Handle CLOB updates (simplified for this implementation)\n    for (const clobData of clobs) {\n      if (clobData.id) {\n        await EContentClob.update(connection, {\n          ...clobData,\n          tenantId,\n        });\n      } else {\n        await EContentClob.create(connection, {\n          ...clobData,\n          contentId: content.id,\n          tenantId,\n        });\n      }\n    }\n\n    await connection.commit();\n    \n    return content;\n  }\n\n  async getEContentContent(\n    context: IContext,\n    id: number\n  ): Promise<IEContentContent> {\n    const { connection, tenantId } = context;\n    \n    return await EContentContent.findBy(connection, { id, tenantId });\n  }\n\n  async getEContentContents(\n    context: IContext,\n    params: {\n      itemId?: number;\n      resourceLanguageId?: number;\n      first?: number;\n      count?: number;\n      searchQuery?: string;\n    }\n  ): Promise<IEContentContent[]> {\n    const { connection, tenantId } = context;\n    console.log('3', tenantId);\n    \n    const criteria: any = { tenantId };\n    if (params.itemId) criteria.itemId = params.itemId;\n    if (params.resourceLanguageId) criteria.resourceLanguageId = params.resourceLanguageId;\n    return await EContentContent.loadBy(connection, criteria, {\n      first: params.first || 0,\n      count: params.count || 10,\n    });\n  }\n\n  async deleteEContentContent(\n    context: IContext,\n    id: number\n  ): Promise<void> {\n    const { connection, tenantId, userId } = context;\n    \n    // Soft delete by updating status\n    await EContentContent.update(connection, {\n      id,\n      tenantId,\n      status: 'DELETED',\n      updatedBy: userId,\n      updatedAt: DateTime.utc(),\n    });\n\n    await connection.commit();\n  }\n}\n", "import { Service } from 'typedi';\nimport hash from 'object-hash';\n\nconst cache: {\n    [key: string]: {\n        time: number,\n        value: any\n    }\n} | {} = {};\n\n@Service()\nexport class HelloService {\n    constructor() {\n    }\n\n    async hello(param1?: string) {\n        return {hello : \"world\"};\n    }\n}\n", "import { IContext } from 'edana-microservice';\nimport Tenant, { ITenant } from '../entities/Tenant';\n\nexport default async function getAllTenants(\n  context: IContext,\n): Promise<ITenant[]> {\n  return Tenant.cached ? await Tenant.cached.load(context) : [];\n}\n", "const EDUCATION = 'EDUCATION';\nconst NON_EDUCATION = 'NON_EDUCATION';\nconst MIXED = 'MIXED';\n\nconst Ids = {\n  [EDUCATION]: 1,\n  [NON_EDUCATION]: 2,\n  [MIXED]: 3,\n};\n\nconst { utils } = require('edana-microservice');\nconst { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(\n  Ids,\n);\n\nmodule.exports = {\n  EDUCATION,\n  NON_EDUCATION,\n  MIXED,\n  serialize,\n  deserialize,\n  valueById,\n  idByValue,\n};\n", "const SWARM = 'SWARM';\n\nconst Ids = {\n  [SWARM]: 1,\n};\n\nconst { utils } = require('edana-microservice');\nconst { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(\n  Ids,\n);\n\nmodule.exports = {\n  SWARM,\n  serialize,\n  deserialize,\n  valueById,\n  idByValue,\n};\n", "module.exports = require(\"dotenv\");", "module.exports = require(\"edana-microservice\");", "module.exports = require(\"express\");", "module.exports = require(\"jsonwebtoken\");", "module.exports = require(\"multer\");", "module.exports = require(\"reflect-metadata\");", "module.exports = require(\"routing-controllers\");", "module.exports = require(\"typedi\");", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import 'reflect-metadata';\nimport { config } from 'dotenv';\nimport express from 'express';\n\n// Load environment variables\nconfig();\nimport { useContainer, useExpressServer } from 'routing-controllers';\nimport { Container } from 'typedi';\nimport { HelloController } from '@webaverse/controllers/hello.controller';\nimport { EContentContentController } from '@webaverse/controllers/eContentContent.controller';\nimport { FilesController } from '@webaverse/controllers/files.controller';\nimport { contextMiddleware } from '@webaverse/middleware/contextMiddleware';\nimport {\n  withUserAuthentication,\n  withTenant,\n  requireAuth,\n  requireTenant\n} from './middleware/authMiddleware';\n\nexport const Search = {\n    start: () => {\n        useContainer(Container);\n\n        const app = express();\n\n        app.use(function (req, res, next) {\n            req.setTimeout(0); // no timeout for all requests, your server will be DoS'd\n            next();\n        });\n\n        app.use(express.urlencoded({ extended: true }));\n        app.use(express.json());\n\n        // Security Configuration\n        const jwtSecret = process.env.JWT_SECRET;\n        if (!jwtSecret) {\n            throw new Error('JWT_SECRET environment variable is required for production security');\n        }\n\n        // Authentication middleware chain (production-ready security)\n        app.use('/api', withUserAuthentication);\n        app.use('/api', withTenant);\n\n        // Security enforcement middleware - REQUIRES VALID AUTHENTICATION\n        app.use('/api', requireAuth);\n        app.use('/api', requireTenant);\n\n        // Apply microservice context middleware after tenant resolution\n        app.use('/api', contextMiddleware);\n\n        // Configure controllers\n        useExpressServer(app, {\n            routePrefix: '/api',\n            controllers: [\n                HelloController,\n                EContentContentController,\n                FilesController\n            ],\n        });\n\n        app.get('*', (req, res, next) => {\n            if (req.path.startsWith('/api')) {\n                next();\n            } else {\n                res.end();\n            }\n        });\n\n        const server = app.listen(process.env.PORT || 3000, () => {\n            // eslint-disable-next-line no-console\n            console.log(`⚡️[server]: Server is running at https://localhost:${process.env.PORT || 3000}`);\n        });\n\n        server.setTimeout(0);\n    },\n};\n\nSearch.start();"], "names": [], "sourceRoot": ""}