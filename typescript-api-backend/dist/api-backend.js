/******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ "./src/constants/fsCategoriesAreas.ts":
/*!********************************************!*\
  !*** ./src/constants/fsCategoriesAreas.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "ACCOUNT_PERSONAL_DRIVE_FILE": () => (/* binding */ ACCOUNT_PERSONAL_DRIVE_FILE),
/* harmony export */   "E_CONTENT_CONTENT_FILE": () => (/* binding */ E_CONTENT_CONTENT_FILE),
/* harmony export */   "E_CONTENT_FOLDER_IMAGE": () => (/* binding */ E_CONTENT_FOLDER_IMAGE),
/* harmony export */   "E_CONTENT_QUESTION_OPTION_FILE": () => (/* binding */ E_CONTENT_QUESTION_OPTION_FILE),
/* harmony export */   "E_CONTENT_QUESTION_TEXTAREA_FILE": () => (/* binding */ E_CONTENT_QUESTION_TEXTAREA_FILE),
/* harmony export */   "E_CONTENT_RESOURCE_FILE": () => (/* binding */ E_CONTENT_RESOURCE_FILE),
/* harmony export */   "IMPORT_SESSION_AREA": () => (/* binding */ IMPORT_SESSION_AREA)
/* harmony export */ });
// File system category areas for different types of attachments
const E_CONTENT_CONTENT_FILE = 'econtent-content-file';
const E_CONTENT_RESOURCE_FILE = 'econtent-resource-image';
const E_CONTENT_FOLDER_IMAGE = 'econtent-folder-image';
const E_CONTENT_QUESTION_OPTION_FILE = 'econtent-question-option-file';
const E_CONTENT_QUESTION_TEXTAREA_FILE = 'econtent-content-textarea-file';
// Other common areas
const ACCOUNT_PERSONAL_DRIVE_FILE = 'account-personal-drive-file';
const IMPORT_SESSION_AREA = 'import-session-area';


/***/ }),

/***/ "./src/controllers/eContentContent.controller.ts":
/*!*******************************************************!*\
  !*** ./src/controllers/eContentContent.controller.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "EContentContentController": () => (/* binding */ EContentContentController)
/* harmony export */ });
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! routing-controllers */ "routing-controllers");
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(routing_controllers__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var multer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! multer */ "multer");
/* harmony import */ var multer__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(multer__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _services_eContentContent_service__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/eContentContent.service */ "./src/services/eContentContent.service.ts");
/* harmony import */ var _middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../middleware/contextMiddleware */ "./src/middleware/contextMiddleware.ts");
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};





// Configure multer for file uploads
const upload = multer__WEBPACK_IMPORTED_MODULE_2___default()({
    storage: multer__WEBPACK_IMPORTED_MODULE_2___default().memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, // 10MB limit
    },
});
// Middleware for file uploads
const fileUploadMiddleware = upload.array('files', 10);
let EContentContentController = class EContentContentController {
    constructor(eContentContentService) {
        this.eContentContentService = eContentContentService;
    }
    async getEContentContents(query, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            const contents = await this.eContentContentService.getEContentContents(context, query);
            return response.json({
                success: true,
                data: contents,
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async getEContentContent(id, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            const content = await this.eContentContentService.getEContentContent(context, id);
            return response.json({
                success: true,
                data: content,
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async createEContentContentSimple(body, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            const content = await this.eContentContentService.createEContentContent(context, body);
            return response.status(201).json({
                success: true,
                data: content,
                message: 'EContentContent created successfully (simple)',
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async createEContentContent(body, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            // Parse the data if it comes from multipart form
            let contentData = body;
            if (typeof body.data === 'string') {
                contentData = JSON.parse(body.data);
            }
            // Handle file uploads from request.files if available
            const files = request.files || [];
            const attachments = files.map((file) => ({
                fileName: file.originalname || file.name,
                fileSize: file.size,
                mimeType: file.mimetype || file.type,
                content: file.buffer || file.data,
            }));
            // Add attachments to the first CLOB if files were uploaded
            if (attachments.length > 0 && contentData.clobs && contentData.clobs.length > 0) {
                contentData.clobs[0].attachments = attachments;
            }
            const content = await this.eContentContentService.createEContentContent(context, contentData);
            return response.status(201).json({
                success: true,
                data: content,
                message: 'EContentContent created successfully',
                filesUploaded: attachments.length,
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async updateEContentContent(id, body, files, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            // Process uploaded files
            const attachments = files ? files.map(file => ({
                fileName: file.originalname,
                fileSize: file.size,
                mimeType: file.mimetype,
                content: file.buffer,
            })) : [];
            // Add attachments to the first CLOB if files were uploaded
            if (attachments.length > 0 && body.clobs && body.clobs.length > 0) {
                body.clobs[0].attachments = attachments;
            }
            const updateData = { ...body, id };
            const content = await this.eContentContentService.updateEContentContent(context, updateData);
            return response.json({
                success: true,
                data: content,
                message: 'EContentContent updated successfully',
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async deleteEContentContent(id, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_4__.getContext)(request);
            await this.eContentContentService.deleteEContentContent(context, id);
            return response.json({
                success: true,
                message: 'EContentContent deleted successfully',
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
};
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Get)('/contents'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.QueryParams)()),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "getEContentContents", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Get)('/contents/:id'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Param)('id')),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "getEContentContent", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Post)('/contents/simple'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Body)()),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "createEContentContentSimple", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Post)('/contents'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Body)()),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "createEContentContent", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Put)('/contents/:id'),
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.UseBefore)(fileUploadMiddleware),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Param)('id')),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Body)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.UploadedFiles)('files')),
    __param(3, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(4, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Array, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "updateEContentContent", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Delete)('/contents/:id'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Param)('id')),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], EContentContentController.prototype, "deleteEContentContent", null);
EContentContentController = __decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.JsonController)('/econtent'),
    (0,typedi__WEBPACK_IMPORTED_MODULE_1__.Service)(),
    __metadata("design:paramtypes", [_services_eContentContent_service__WEBPACK_IMPORTED_MODULE_3__.EContentContentService])
], EContentContentController);



/***/ }),

/***/ "./src/controllers/files.controller.ts":
/*!*********************************************!*\
  !*** ./src/controllers/files.controller.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "FilesController": () => (/* binding */ FilesController)
/* harmony export */ });
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! routing-controllers */ "routing-controllers");
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(routing_controllers__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../middleware/contextMiddleware */ "./src/middleware/contextMiddleware.ts");
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};



let FilesController = class FilesController {
    async getFile(fileId, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_2__.getContext)(request);
            const fsClient = await context.fsClient;
            // Get file metadata
            const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);
            if (!fileInfo) {
                return response.status(404).json({
                    success: false,
                    error: 'File not found',
                });
            }
            // For this mock implementation, we'll return file metadata
            // In a real implementation, you would stream the actual file content
            return response.json({
                success: true,
                data: {
                    fileId: fileInfo.fileId,
                    fileName: fileInfo.fileName,
                    fileSize: fileInfo.fileSize,
                    mimeType: fileInfo.mimeType,
                    url: fileInfo.url,
                },
            });
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
    async downloadFile(fileId, request, response) {
        try {
            const context = (0,_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_2__.getContext)(request);
            const fsClient = await context.fsClient;
            const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);
            if (!fileInfo) {
                return response.status(404).json({
                    success: false,
                    error: 'File not found',
                });
            }
            // Set appropriate headers for file download
            response.setHeader('Content-Type', fileInfo.mimeType);
            response.setHeader('Content-Disposition', `attachment; filename="${fileInfo.fileName}"`);
            response.setHeader('Content-Length', fileInfo.fileSize);
            // For this mock implementation, return a simple text response
            // In a real implementation, you would stream the actual file
            return response.send(`Mock file content for ${fileInfo.fileName}`);
        }
        catch (error) {
            return response.status(500).json({
                success: false,
                error: error.message,
            });
        }
    }
};
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Get)('/:fileId'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Param)('fileId')),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "getFile", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Get)('/:fileId/download'),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Param)('fileId')),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Req)()),
    __param(2, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Object, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "downloadFile", null);
FilesController = __decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.JsonController)('/files'),
    (0,typedi__WEBPACK_IMPORTED_MODULE_1__.Service)()
], FilesController);



/***/ }),

/***/ "./src/controllers/hello.controller.ts":
/*!*********************************************!*\
  !*** ./src/controllers/hello.controller.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "HelloController": () => (/* binding */ HelloController)
/* harmony export */ });
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! routing-controllers */ "routing-controllers");
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(routing_controllers__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _webaverse_services_hello_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @webaverse/services/hello.service */ "./src/services/hello.service.ts");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_2__);
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (undefined && undefined.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};



let HelloController = class HelloController {
    constructor(helloService) {
        this.helloService = helloService;
    }
    async getRequest(query, response) {
        return response.send(await this.helloService.hello());
    }
    async postReqeust(query, response) {
        return response.send(await this.helloService.hello());
    }
};
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Get)(''),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.QueryParams)()),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], HelloController.prototype, "getRequest", null);
__decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Post)(''),
    __param(0, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.QueryParams)()),
    __param(1, (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], HelloController.prototype, "postReqeust", null);
HelloController = __decorate([
    (0,routing_controllers__WEBPACK_IMPORTED_MODULE_0__.JsonController)('/hello'),
    (0,typedi__WEBPACK_IMPORTED_MODULE_2__.Service)(),
    __metadata("design:paramtypes", [_webaverse_services_hello_service__WEBPACK_IMPORTED_MODULE_1__.HelloService])
], HelloController);



/***/ }),

/***/ "./src/entities/EContentClob.ts":
/*!**************************************!*\
  !*** ./src/entities/EContentClob.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_0__);

const { tenantValidator } = edana_microservice__WEBPACK_IMPORTED_MODULE_0__.validators;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new edana_microservice__WEBPACK_IMPORTED_MODULE_0__.Entity('EContentClob', {
    tenantId: 'number',
    resourceId: 'number',
    itemId: 'number',
    contentId: 'number',
    languageId: 'number',
    resourceAttributeId: 'number',
    attributeId: 'number',
    content: 'clob',
    groupSequence: 'number',
    contentClobTypeId: 'number',
    resourceCategoryId: 'number',
}, {}, { tableName: 'ECN_CONTENT_CLOB' }, {
    validators: [
        {
            actions: ['update'],
            validators: [
                {
                    validate: tenantValidator,
                    params: ({ id, tenantId }) => ({ id, tenantId }),
                    errorMessage: 'tenantAccessError',
                },
            ],
        },
    ],
}));


/***/ }),

/***/ "./src/entities/EContentContent.ts":
/*!*****************************************!*\
  !*** ./src/entities/EContentContent.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_0__);

const { tenantValidator } = edana_microservice__WEBPACK_IMPORTED_MODULE_0__.validators;
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new edana_microservice__WEBPACK_IMPORTED_MODULE_0__.Entity('EContentContent', {
    tenantId: 'number',
    itemId: 'number',
    resourceLanguageId: 'number',
    description: 'string',
    createdBy: 'string',
    createdAt: 'datetime',
    updatedBy: 'number',
    updatedAt: 'datetime',
    sequence: 'number',
    status: 'number',
    headingSearchTerm: 'string',
}, {}, { tableName: 'ECN_CONTENT' }, {
    validators: [
        {
            actions: ['update'],
            validators: [
                {
                    validate: tenantValidator,
                    params: ({ id, tenantId }) => ({ id, tenantId }),
                    errorMessage: 'tenantAccessError',
                },
            ],
        },
    ],
}));


/***/ }),

/***/ "./src/entities/Tenant.ts":
/*!********************************!*\
  !*** ./src/entities/Tenant.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _models_TenantInstitutionType__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../models/TenantInstitutionType */ "./src/models/TenantInstitutionType.js");
/* harmony import */ var _models_TenantInstitutionType__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_models_TenantInstitutionType__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _models_TenantPlatform__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../models/TenantPlatform */ "./src/models/TenantPlatform.js");
/* harmony import */ var _models_TenantPlatform__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_models_TenantPlatform__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _models_TenantStatus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../models/TenantStatus */ "./src/models/TenantStatus.ts");




const Tenant = new edana_microservice__WEBPACK_IMPORTED_MODULE_0__.Entity("Tenant", {
    domain: "string",
    clientAbbreviation: "string",
    siteLink: "string",
    licenseKey: "string",
    siteName: "string",
    logoFileId: "number",
    loginBgImgId: "number",
    appLoginBgImgId: "number",
    loginPageMessage: "string",
    createdAt: "date",
    status: {
        preset: "number",
        serialize: _models_TenantStatus__WEBPACK_IMPORTED_MODULE_3__["default"].serialize,
        deserialize: _models_TenantStatus__WEBPACK_IMPORTED_MODULE_3__["default"].deserialize,
        canUpdate: true,
    },
    platform: {
        columnName: "PLATFORM_ID",
        preset: "number",
        serialize: (_models_TenantPlatform__WEBPACK_IMPORTED_MODULE_2___default().serialize),
        deserialize: (_models_TenantPlatform__WEBPACK_IMPORTED_MODULE_2___default().deserialize),
        canUpdate: true,
    },
    institutionType: {
        columnName: "INSTITUTION_TYPE_ID",
        preset: "number",
        serialize: (_models_TenantInstitutionType__WEBPACK_IMPORTED_MODULE_1___default().serialize),
        deserialize: (_models_TenantInstitutionType__WEBPACK_IMPORTED_MODULE_1___default().deserialize),
        canUpdate: true,
    },
    countryId: "number",
    licenseName: "string",
}, {}, {
    tableName: "C_TENANT",
    cache: {
        key: "Tenant",
        list: true,
        loader: async (connection) => await Tenant.loadAll(connection),
    },
}, {});
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Tenant);


/***/ }),

/***/ "./src/helpers/token.ts":
/*!******************************!*\
  !*** ./src/helpers/token.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "extractToken": () => (/* binding */ extractToken),
/* harmony export */   "getDomain": () => (/* binding */ getDomain),
/* harmony export */   "getTokenFromRequest": () => (/* binding */ getTokenFromRequest)
/* harmony export */ });
/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ "jsonwebtoken");
/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);

/**
 * Extract JWT token from Express request with proper audience validation
 * Production-ready version with security checks
 */
function extractToken(req, token) {
    return new Promise((resolve, reject) => {
        const secret = process.env.JWT_SECRET;
        if (!secret) {
            reject(new Error("JWT_SECRET environment variable is required"));
            return;
        }
        // Get audience from request domain
        const domain = getDomain(req);
        // For Bearer tokens, use the token directly
        // For cookie tokens, extract from cookie string
        let jwtToken = token;
        if (token && token.includes('=')) {
            // This looks like a cookie string, extract the JWT
            const authKey = `Authorization_${domain}`;
            jwtToken = extractJwt(token, authKey);
        }
        if (!jwtToken) {
            resolve(null);
            return;
        }
        jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(jwtToken, secret, { audience: domain }, (err, payload) => {
            if (err) {
                if (err instanceof (jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().TokenExpiredError) ||
                    err.message.includes("audience invalid")) {
                    resolve(null);
                }
                else {
                    reject(err);
                }
            }
            else {
                resolve(payload);
            }
        });
    });
}
function isDev() {
    return process.env.ENV === "development";
}
function getDomain(req) {
    return isDev()
        ? process.env.AUDIENCE_TEMPLATE || req.get("host") || "localhost"
        : req.get("host") || "localhost";
}
function extractJwt(cookieString, key) {
    if (!cookieString)
        return null;
    // Split cookies by ;
    const cookies = cookieString.split(";").map((c) => c.trim());
    // Find the one that starts with the given key
    const target = cookies.find((c) => c.startsWith(key + "="));
    if (!target)
        return null;
    // Return the value after =
    return target.split("=")[1] || null;
}
/**
 * Extract token from Authorization header or signed cookies
 * Production-ready version with proper cookie handling
 */
function getTokenFromRequest(req) {
    var _a, _b;
    // Try Authorization header first (Bearer token)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
        return authHeader.substring(7);
    }
    // Try signed cookies (production approach)
    if (req.signedCookies) {
        const domain = getDomain(req);
        const authKey = `Authorization_${domain}`;
        if (req.signedCookies[authKey]) {
            return req.signedCookies[authKey];
        }
    }
    // Fallback to regular cookies for development
    const cookieToken = (_b = (_a = req.headers.cookie) === null || _a === void 0 ? void 0 : _a.split(";").find((c) => c.trim().startsWith("auth_token="))) === null || _b === void 0 ? void 0 : _b.split("=")[1];
    return cookieToken;
}


/***/ }),

/***/ "./src/middleware/authMiddleware.ts":
/*!******************************************!*\
  !*** ./src/middleware/authMiddleware.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "requireAuth": () => (/* binding */ requireAuth),
/* harmony export */   "requireTenant": () => (/* binding */ requireTenant),
/* harmony export */   "withCookieUserToken": () => (/* binding */ withCookieUserToken),
/* harmony export */   "withSubstitutionAuthentication": () => (/* binding */ withSubstitutionAuthentication),
/* harmony export */   "withTenant": () => (/* binding */ withTenant),
/* harmony export */   "withUserAuthentication": () => (/* binding */ withUserAuthentication)
/* harmony export */ });
/* harmony import */ var _helpers_token__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../helpers/token */ "./src/helpers/token.ts");
/* harmony import */ var _services_tenant_service__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../services/tenant.service */ "./src/services/tenant.service.ts");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_2__);

// import { find, get } from "lodash"; // Not needed with simplified tenant lookup


/**
 * Create a temporary context for tenant resolution
 * This allows us to query the database before the main context is created
 */
async function createTemporaryContext() {
    return new Promise((resolve, reject) => {
        // Create a minimal Koa context for withConnection
        const mockKoaCtx = {
            state: {
                id: `tenant_lookup_${Date.now()}`,
                log: {
                    info: (message, ...args) => console.log('TENANT_LOOKUP INFO:', message, ...args),
                    error: (message, ...args) => console.error('TENANT_LOOKUP ERROR:', message, ...args),
                    warn: (message, ...args) => console.warn('TENANT_LOOKUP WARN:', message, ...args),
                    debug: (message, ...args) => console.log('TENANT_LOOKUP DEBUG:', message, ...args),
                    silly: (message, ...args) => console.log('TENANT_LOOKUP SILLY:', message, ...args),
                    verbose: (message, ...args) => console.log('TENANT_LOOKUP VERBOSE:', message, ...args),
                },
                tenantId: 1,
                organisationGroupId: 1,
                userId: 1,
                userName: 'system_tenant_lookup',
                accessHash: 'system_lookup',
                token: 'system_lookup',
            },
            customProperties: {},
        };
        (0,edana_microservice__WEBPACK_IMPORTED_MODULE_2__.withConnection)(mockKoaCtx, async (connection) => {
            console.log('Temporary context created for tenant lookup');
            // Create a simple cache for tenant lookup
            const tenantCache = new Map();
            // Create a minimal context with just what we need for tenant lookup
            const tempContext = {
                connection,
                cache: {
                    wrap: async (key, loader) => {
                        if (tenantCache.has(key)) {
                            console.log(`Tenant cache hit: ${key}`);
                            return tenantCache.get(key);
                        }
                        console.log(`Tenant cache miss: ${key}`);
                        const result = await loader();
                        tenantCache.set(key, result);
                        return result;
                    },
                    invalidate: async (key) => {
                        tenantCache.delete(key);
                    }
                },
                tenantId: 1,
                userId: 1,
                userName: 'system_tenant_lookup',
                log: mockKoaCtx.state.log,
            };
            resolve(tempContext);
        }).catch((error) => {
            console.error('Failed to create temporary context for tenant lookup:', error);
            reject(error);
        });
    });
}
/**
 * Express middleware equivalent to withCookieUserToken from edana2_api
 * Extracts JWT token from signed cookies
 */
const withCookieUserToken = async (req, res, next) => {
    req.authState = req.authState || {};
    // Extract token from signed cookies (production-ready)
    const cookieSecret = process.env.COOKIE_SECRET;
    if (!cookieSecret) {
        res.status(500).json({
            success: false,
            error: "Server configuration error: COOKIE_SECRET not set",
        });
        return;
    }
    // Get domain-specific cookie key
    const domain = (0,_helpers_token__WEBPACK_IMPORTED_MODULE_0__.getDomain)(req);
    const authKey = `Authorization_${domain}`;
    // Extract token from signed cookie
    if (req.signedCookies && req.signedCookies[authKey]) {
        req.authState.token = req.signedCookies[authKey];
    }
    next();
};
/**
 * Express middleware equivalent to withSubstitutionAuthentication from edana2_api
 * Checks for substitution authentication via signed cookies
 */
const withSubstitutionAuthentication = async (req, res, next) => {
    req.authState = req.authState || {};
    if (typeof req.authState.isSubstitution === "undefined") {
        const domain = (0,_helpers_token__WEBPACK_IMPORTED_MODULE_0__.getDomain)(req);
        const originAuthKey = `OriginalAuthorization_${domain}`;
        req.authState.isSubstitution = !!(req.signedCookies && req.signedCookies[originAuthKey]);
    }
    next();
};
/**
 * Express middleware equivalent to withUserAuthentication from edana2_api
 * Extracts and validates JWT token, populates user information
 */
const withUserAuthentication = async (req, res, next) => {
    req.authState = req.authState || {};
    // Extract token from request (Authorization header or cookies)
    const token = req.authState.token || (0,_helpers_token__WEBPACK_IMPORTED_MODULE_0__.getTokenFromRequest)(req);
    if (token) {
        req.authState.token = token;
        try {
            const payload = await (0,_helpers_token__WEBPACK_IMPORTED_MODULE_0__.extractToken)(req, token);
            if (payload && payload.userId) {
                req.authState.userId = payload.userId;
                req.authState.userName = payload.userName;
                req.authState.accessHash = payload.accessHash;
                req.authState.organisationGroupId = payload.organisationGroupId;
                if (payload.tenantId) {
                    req.authState.tenantId = payload.tenantId;
                }
            }
        }
        catch (e) {
            console.warn("Invalid JWT token:", e);
            res.status(403).json({
                success: false,
                error: "Invalid authentication token",
            });
            return;
        }
    }
    next();
};
/**
 * Express middleware equivalent to withTenant from edana2_api
 * Resolves tenant information from domain using database/cache lookup
 */
const withTenant = async (req, _res, next) => {
    req.authState = req.authState || {};
    try {
        // Get domain from request
        const domain = (0,_helpers_token__WEBPACK_IMPORTED_MODULE_0__.getDomain)(req);
        console.log('Resolving tenant for domain:', domain);
        // Create temporary context for tenant lookup
        const tempContext = await createTemporaryContext();
        // Fetch all tenants from database/cache
        const tenants = await (0,_services_tenant_service__WEBPACK_IMPORTED_MODULE_1__["default"])(tempContext);
        console.log(`Loaded ${tenants.length} tenants for domain matching`);
        // Find tenant by domain
        const tenant = tenants.find((t) => t.domain === domain);
        if (tenant && tenant.status === 'ACTIVE') { // Active tenant
            req.authState.tenantId = tenant.id;
            console.log(`Found active tenant: ${tenant.siteName} (ID: ${tenant.id}) for domain: ${domain}`);
        }
        else {
            // Fallback to JWT token tenant or default
            const fallbackTenantId = req.authState.tenantId || 2;
            req.authState.tenantId = fallbackTenantId;
            console.log(`No active tenant found for domain: ${domain}, using fallback tenant ID: ${fallbackTenantId}`);
        }
    }
    catch (error) {
        console.error('Error in tenant resolution:', error);
        // Error fallback - use JWT token tenant or default
        const fallbackTenantId = req.authState.tenantId || 2;
        req.authState.tenantId = fallbackTenantId;
        console.log(`Error fallback: using tenant ID: ${fallbackTenantId}`);
    }
    next();
};
/**
 * Middleware to ensure user is authenticated
 */
const requireAuth = (req, res, next) => {
    var _a;
    if (!((_a = req.authState) === null || _a === void 0 ? void 0 : _a.userId)) {
        res.status(401).json({ error: "Authentication required" });
        return;
    }
    next();
};
/**
 * Middleware to ensure tenant is resolved
 */
const requireTenant = (req, res, next) => {
    var _a;
    if (!((_a = req.authState) === null || _a === void 0 ? void 0 : _a.tenantId)) {
        res.status(403).json({ error: "Invalid tenant" });
        return;
    }
    next();
};


/***/ }),

/***/ "./src/middleware/contextMiddleware.ts":
/*!*********************************************!*\
  !*** ./src/middleware/contextMiddleware.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "WithContext": () => (/* binding */ WithContext),
/* harmony export */   "contextMiddleware": () => (/* binding */ contextMiddleware),
/* harmony export */   "getContext": () => (/* binding */ getContext)
/* harmony export */ });
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_0__);

/**
 * Create a real Oracle database connection using the edana-microservice framework
 * This uses the withConnection function which handles connection pooling automatically
 */
async function createRealOracleConnection(tenantId, userId, organisationGroupId) {
    // Create a mock Koa context for the withConnection function
    const mockKoaCtx = {
        state: {
            id: `req_${Date.now()}`,
            log: {
                info: (message, ...args) => console.log('INFO:', message, ...args),
                error: (message, ...args) => console.error('ERROR:', message, ...args),
                warn: (message, ...args) => console.warn('WARN:', message, ...args),
                debug: (message, ...args) => console.log('DEBUG:', message, ...args),
                silly: (message, ...args) => console.log('SILLY:', message, ...args),
                verbose: (message, ...args) => console.log('VERBOSE:', message, ...args),
            },
            tenantId,
            organisationGroupId,
            userId,
            userName: `user_${userId}`,
            accessHash: 'mock_access_hash',
            token: 'mock_token',
        },
        customProperties: {},
    };
    return new Promise((resolve, reject) => {
        (0,edana_microservice__WEBPACK_IMPORTED_MODULE_0__.withConnection)(mockKoaCtx, async (connection) => {
            console.log('Real Oracle connection established via edana-microservice');
            resolve(connection);
        }).catch((error) => {
            console.error('Failed to create real Oracle connection:', error);
            // Fallback to a mock connection if Oracle connection fails
            const mockConnection = {
                async commit() {
                    console.log('FALLBACK: Transaction committed');
                },
                async rollback() {
                    console.log('FALLBACK: Transaction rolled back');
                },
                execute: async (sql, binds, options) => {
                    console.log('FALLBACK: Mock database execute:', sql);
                    return { rows: [], outBinds: {} };
                },
                executeMany: async (sql, binds, options) => {
                    console.log('FALLBACK: Mock database executeMany:', sql);
                    return { rowsAffected: 0 };
                },
                executeAll: async (query) => {
                    console.log('FALLBACK: Mock database executeAll:', query);
                },
                close: async () => {
                    console.log('FALLBACK: Mock database close');
                },
            };
            resolve(mockConnection);
        });
    });
}
// Mock cache implementation
const mockCache = {
    wrap: async (key, fn) => {
        console.log(`Cache lookup for key: ${key}`);
        return await fn();
    },
    get: async (key) => {
        console.log(`Cache get for key: ${key}`);
        return null;
    },
    set: async (key, value, ttl) => {
        console.log(`Cache set for key: ${key}, TTL: ${ttl}`);
    },
    del: async (key) => {
        console.log(`Cache delete for key: ${key}`);
    },
    clear: async () => {
        console.log('Cache clear');
    },
};
// Mock logger
const mockLogger = {
    info: (message, ...args) => console.log('INFO:', message, ...args),
    error: (message, ...args) => console.error('ERROR:', message, ...args),
    warn: (message, ...args) => console.warn('WARN:', message, ...args),
    debug: (message, ...args) => console.log('DEBUG:', message, ...args),
    silly: (message, ...args) => console.log('SILLY:', message, ...args),
    verbose: (message, ...args) => console.log('VERBOSE:', message, ...args),
};
// Service client registry
const serviceClients = new Map();
// Register mock service clients
serviceClients.set('edana_api_fs', {
    files: {
        deleteFiles: async (params) => {
            console.log('Service client: deleting files', params.fileIds);
        },
    },
});
serviceClients.set('edana2_api_notf', {
    notifications: {
        send: async (params) => {
            console.log('Service client: sending notification', params);
        },
    },
});
/**
 * Middleware to create and attach microservice context to requests
 */
const contextMiddleware = async (req, res, next) => {
    var _a, _b, _c, _d;
    try {
        // Extract tenant and user information from auth middleware or headers
        const tenantId = ((_a = req.authState) === null || _a === void 0 ? void 0 : _a.tenantId) || 2; // Default fallback
        const organisationGroupId = ((_b = req.authState) === null || _b === void 0 ? void 0 : _b.organisationGroupId) || 1; // Default fallback
        const userId = ((_c = req.authState) === null || _c === void 0 ? void 0 : _c.userId) || 1; // Default fallback
        const userName = ((_d = req.authState) === null || _d === void 0 ? void 0 : _d.userName) || `user_${userId}`;
        // Create a real Oracle database connection using edana-microservice
        const oracleConnection = await createRealOracleConnection(tenantId, userId, organisationGroupId);
        // Create base context matching the real IContext interface
        const baseContext = {
            connection: oracleConnection,
            cache: mockCache,
            tenantId,
            isSuperTenant: false,
            organisationGroupId,
            organisationsIds: [organisationGroupId],
            userId,
            userName,
            log: mockLogger,
            redisConnectionFactory: null,
            requireServiceClient: async (serviceName) => {
                const client = serviceClients.get(serviceName);
                if (!client) {
                    throw new Error(`Service client not found: ${serviceName}`);
                }
                return client;
            },
            raiseHumanError: (errors) => { throw new Error('Human error'); },
            raiseInvalidRequestError: (description) => { throw new Error(description); },
            raiseNotAuthenticatedError: () => { throw new Error('Not authenticated'); },
            raiseNotAuthorizedError: () => { throw new Error('Not authorized'); },
            raiseNotFoundError: (entity) => { throw new Error(`${entity.name} not found`); },
            raiseInternalServerError: () => { throw new Error('Internal server error'); },
            raiseNotImplementedError: () => { throw new Error('Not implemented'); },
            raiseServiceUnavailableError: () => { throw new Error('Service unavailable'); },
            raiseNotUniqueError: () => { throw new Error('Not unique'); },
            ctx: {},
        };
        // Create a mock fsClient since the real withFsClient is designed for Koa
        // In a real implementation, you would integrate with the actual file service
        const fsClient = Promise.resolve({
            addEntityAttachments: async (fileCategoryArea, referenceId, attachmentInputs) => {
                console.log('Mock fsClient: addEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs });
                return attachmentInputs.map((_, index) => ({ id: index + 1 }));
            },
            getFileCategoryInfo: async () => {
                console.log('Mock fsClient: getFileCategoryInfo');
                return { fileCategories: [], categoryArea: [], categoryGroups: [] };
            },
            getStorageInstances: async (params) => {
                console.log('Mock fsClient: getStorageInstances', params);
                return [];
            },
            initiateAttachmentUpload: async (fileCategoryAreaKey, files) => {
                console.log('Mock fsClient: initiateAttachmentUpload', { fileCategoryAreaKey, files });
                return files.map(() => ({ ok: true, uploadToken: 'mock-token', uploadUrl: 'mock-url', uploadTarget: 'app', uploadConfig: {} }));
            },
            loadEntityAttachmentByFileId: async (fileId) => {
                console.log('Mock fsClient: loadEntityAttachmentByFileId', fileId);
                return {
                    id: fileId,
                    fileId,
                    fileMetadataId: fileId,
                    status: 'A',
                    version: 1,
                    fileName: `file_${fileId}.txt`,
                    fileSize: 1024,
                    mimeType: 'text/plain',
                    url: `http://localhost:3001/files/${fileId}/download`,
                };
            },
            loadEntityAttachments: async (fileCategoryArea, referenceId, options) => {
                console.log('Mock fsClient: loadEntityAttachments', { fileCategoryArea, referenceId, options });
                return [];
            },
            bulkLoadEntityAttachments: async (fileCategoryArea, referenceIds, options) => {
                console.log('Mock fsClient: bulkLoadEntityAttachments', { fileCategoryArea, referenceIds, options });
                return [];
            },
            bulkLoadEntityAttachmentsCount: async (fileCategoryArea, referenceIds, options) => {
                console.log('Mock fsClient: bulkLoadEntityAttachmentsCount', { fileCategoryArea, referenceIds, options });
                return 0;
            },
            updateEntityAttachments: async (fileCategoryArea, referenceId, attachmentInputs, metaInfo) => {
                console.log('Mock fsClient: updateEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs, metaInfo });
                return attachmentInputs.map((_, index) => index + 1);
            },
            resolveBatchEntityFileSize: async (...args) => {
                console.log('Mock fsClient: resolveBatchEntityFileSize', args);
                return {};
            },
            resolveBatchEntityFileUrl: async (...args) => {
                console.log('Mock fsClient: resolveBatchEntityFileUrl', args);
                return {};
            },
        });
        // Create the complete context
        const contextWithFs = {
            ...baseContext,
            fsClient,
        };
        // Attach context to request
        req.context = contextWithFs;
        next();
    }
    catch (error) {
        console.error('Context middleware error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to initialize request context',
        });
    }
};
/**
 * Helper function to get context from request
 */
const getContext = (req) => {
    if (!req.context) {
        throw new Error('Context not available. Ensure contextMiddleware is applied.');
    }
    return req.context;
};
/**
 * Decorator to inject context into controller methods
 */
const WithContext = (target, propertyName, descriptor) => {
    const method = descriptor.value;
    descriptor.value = function (...args) {
        // Find the request object in the arguments
        const req = args.find(arg => arg && arg.context);
        if (req && req.context) {
            // Replace the mock context creation with the real context
            return method.apply(this, args);
        }
        return method.apply(this, args);
    };
    return descriptor;
};


/***/ }),

/***/ "./src/models/TenantStatus.ts":
/*!************************************!*\
  !*** ./src/models/TenantStatus.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "DELETED": () => (/* binding */ DELETED),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_0__);

const CREATION_IN_PROGRESS = 'CREATION_IN_PROGRESS';
const ACTIVE = 'ACTIVE';
const INACTIVE = 'INACTIVE';
const DELETED = 'DELETED';
const REMOVING = 'REMOVING';
const DEPLOY_FAILED = 'DEPLOY_FAILED';
const Ids = {
    [CREATION_IN_PROGRESS]: 0,
    [ACTIVE]: 1,
    [REMOVING]: 2,
    [INACTIVE]: -1,
    [DELETED]: -2,
    [DEPLOY_FAILED]: -3,
};
const { valueById, idByValue, serialize, deserialize } = edana_microservice__WEBPACK_IMPORTED_MODULE_0__.utils.getModelCommon(Ids);
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({
    CREATION_IN_PROGRESS,
    ACTIVE,
    INACTIVE,
    DELETED,
    REMOVING,
    DEPLOY_FAILED,
    valueById,
    idByValue,
    serialize,
    deserialize,
});


/***/ }),

/***/ "./src/services/eContentContent.service.ts":
/*!*************************************************!*\
  !*** ./src/services/eContentContent.service.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "EContentContentService": () => (/* binding */ EContentContentService)
/* harmony export */ });
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! edana-microservice */ "edana-microservice");
/* harmony import */ var edana_microservice__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(edana_microservice__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../entities/EContentContent */ "./src/entities/EContentContent.ts");
/* harmony import */ var _entities_EContentClob__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../entities/EContentClob */ "./src/entities/EContentClob.ts");
/* harmony import */ var _constants_fsCategoriesAreas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../constants/fsCategoriesAreas */ "./src/constants/fsCategoriesAreas.ts");
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};





let EContentContentService = class EContentContentService {
    async createEContentContent(context, params) {
        const { connection, tenantId, organisationGroupId, userId } = context;
        const fsClient = await context.fsClient;
        // Extract clobs and other data
        const { clobs, questionOptions, ...contentData } = params;
        // Create the main content entity
        const content = await _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__["default"].create(connection, {
            ...contentData,
            tenantId,
            createdBy: userId,
            createdAt: edana_microservice__WEBPACK_IMPORTED_MODULE_1__.DateTime.utc(),
            updatedBy: userId,
            updatedAt: edana_microservice__WEBPACK_IMPORTED_MODULE_1__.DateTime.utc(),
        });
        // Process CLOBs with file attachments
        for (const clobData of clobs) {
            const { attachments = [], ...clobFields } = clobData;
            // Create the CLOB entity
            const clob = await _entities_EContentClob__WEBPACK_IMPORTED_MODULE_3__["default"].create(connection, {
                ...clobFields,
                contentId: content.id,
                tenantId,
            });
            // Handle file attachments if any
            if (attachments.length > 0) {
                await fsClient.updateEntityAttachments(_constants_fsCategoriesAreas__WEBPACK_IMPORTED_MODULE_4__.E_CONTENT_CONTENT_FILE, clob.id, attachments, { tenantId, organisationGroupId });
            }
        }
        // Commit transaction
        await connection.commit();
        return content;
    }
    async updateEContentContent(context, params) {
        const { connection, tenantId, userId } = context;
        const { clobs, questionOptions, ...contentData } = params;
        // Update the main content entity
        const content = await _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__["default"].update(connection, {
            ...contentData,
            tenantId,
            updatedBy: userId,
            updatedAt: edana_microservice__WEBPACK_IMPORTED_MODULE_1__.DateTime.utc(),
        });
        // Handle CLOB updates (simplified for this implementation)
        for (const clobData of clobs) {
            if (clobData.id) {
                await _entities_EContentClob__WEBPACK_IMPORTED_MODULE_3__["default"].update(connection, {
                    ...clobData,
                    tenantId,
                });
            }
            else {
                await _entities_EContentClob__WEBPACK_IMPORTED_MODULE_3__["default"].create(connection, {
                    ...clobData,
                    contentId: content.id,
                    tenantId,
                });
            }
        }
        await connection.commit();
        return content;
    }
    async getEContentContent(context, id) {
        const { connection, tenantId } = context;
        return await _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__["default"].findBy(connection, { id, tenantId });
    }
    async getEContentContents(context, params) {
        const { connection, tenantId } = context;
        console.log('3', tenantId);
        const criteria = { tenantId };
        if (params.itemId)
            criteria.itemId = params.itemId;
        if (params.resourceLanguageId)
            criteria.resourceLanguageId = params.resourceLanguageId;
        return await _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__["default"].loadBy(connection, criteria, {
            first: params.first || 0,
            count: params.count || 10,
        });
    }
    async deleteEContentContent(context, id) {
        const { connection, tenantId, userId } = context;
        // Soft delete by updating status
        await _entities_EContentContent__WEBPACK_IMPORTED_MODULE_2__["default"].update(connection, {
            id,
            tenantId,
            status: 'DELETED',
            updatedBy: userId,
            updatedAt: edana_microservice__WEBPACK_IMPORTED_MODULE_1__.DateTime.utc(),
        });
        await connection.commit();
    }
};
EContentContentService = __decorate([
    (0,typedi__WEBPACK_IMPORTED_MODULE_0__.Service)()
], EContentContentService);



/***/ }),

/***/ "./src/services/hello.service.ts":
/*!***************************************!*\
  !*** ./src/services/hello.service.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "HelloService": () => (/* binding */ HelloService)
/* harmony export */ });
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_0__);
var __decorate = (undefined && undefined.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (undefined && undefined.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};

const cache = {};
let HelloService = class HelloService {
    constructor() {
    }
    async hello(param1) {
        return { hello: "world" };
    }
};
HelloService = __decorate([
    (0,typedi__WEBPACK_IMPORTED_MODULE_0__.Service)(),
    __metadata("design:paramtypes", [])
], HelloService);



/***/ }),

/***/ "./src/services/tenant.service.ts":
/*!****************************************!*\
  !*** ./src/services/tenant.service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ getAllTenants)
/* harmony export */ });
/* harmony import */ var _entities_Tenant__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../entities/Tenant */ "./src/entities/Tenant.ts");

async function getAllTenants(context) {
    return _entities_Tenant__WEBPACK_IMPORTED_MODULE_0__["default"].cached ? await _entities_Tenant__WEBPACK_IMPORTED_MODULE_0__["default"].cached.load(context) : [];
}


/***/ }),

/***/ "./src/models/TenantInstitutionType.js":
/*!*********************************************!*\
  !*** ./src/models/TenantInstitutionType.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const EDUCATION = 'EDUCATION';
const NON_EDUCATION = 'NON_EDUCATION';
const MIXED = 'MIXED';

const Ids = {
  [EDUCATION]: 1,
  [NON_EDUCATION]: 2,
  [MIXED]: 3,
};

const { utils } = __webpack_require__(/*! edana-microservice */ "edana-microservice");
const { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(
  Ids,
);

module.exports = {
  EDUCATION,
  NON_EDUCATION,
  MIXED,
  serialize,
  deserialize,
  valueById,
  idByValue,
};


/***/ }),

/***/ "./src/models/TenantPlatform.js":
/*!**************************************!*\
  !*** ./src/models/TenantPlatform.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const SWARM = 'SWARM';

const Ids = {
  [SWARM]: 1,
};

const { utils } = __webpack_require__(/*! edana-microservice */ "edana-microservice");
const { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(
  Ids,
);

module.exports = {
  SWARM,
  serialize,
  deserialize,
  valueById,
  idByValue,
};


/***/ }),

/***/ "dotenv":
/*!*************************!*\
  !*** external "dotenv" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("dotenv");

/***/ }),

/***/ "edana-microservice":
/*!*************************************!*\
  !*** external "edana-microservice" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("edana-microservice");

/***/ }),

/***/ "express":
/*!**************************!*\
  !*** external "express" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("express");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("jsonwebtoken");

/***/ }),

/***/ "multer":
/*!*************************!*\
  !*** external "multer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("multer");

/***/ }),

/***/ "reflect-metadata":
/*!***********************************!*\
  !*** external "reflect-metadata" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("reflect-metadata");

/***/ }),

/***/ "routing-controllers":
/*!**************************************!*\
  !*** external "routing-controllers" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("routing-controllers");

/***/ }),

/***/ "typedi":
/*!*************************!*\
  !*** external "typedi" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("typedi");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry need to be wrapped in an IIFE because it need to be in strict mode.
(() => {
"use strict";
/*!**********************!*\
  !*** ./src/index.ts ***!
  \**********************/
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "Search": () => (/* binding */ Search)
/* harmony export */ });
/* harmony import */ var reflect_metadata__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! reflect-metadata */ "reflect-metadata");
/* harmony import */ var reflect_metadata__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(reflect_metadata__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dotenv */ "dotenv");
/* harmony import */ var dotenv__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dotenv__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var express__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! express */ "express");
/* harmony import */ var express__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(express__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! routing-controllers */ "routing-controllers");
/* harmony import */ var routing_controllers__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(routing_controllers__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! typedi */ "typedi");
/* harmony import */ var typedi__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(typedi__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _webaverse_controllers_hello_controller__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @webaverse/controllers/hello.controller */ "./src/controllers/hello.controller.ts");
/* harmony import */ var _webaverse_controllers_eContentContent_controller__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @webaverse/controllers/eContentContent.controller */ "./src/controllers/eContentContent.controller.ts");
/* harmony import */ var _webaverse_controllers_files_controller__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @webaverse/controllers/files.controller */ "./src/controllers/files.controller.ts");
/* harmony import */ var _webaverse_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @webaverse/middleware/contextMiddleware */ "./src/middleware/contextMiddleware.ts");
/* harmony import */ var _middleware_authMiddleware__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./middleware/authMiddleware */ "./src/middleware/authMiddleware.ts");



// Load environment variables
(0,dotenv__WEBPACK_IMPORTED_MODULE_1__.config)();







const Search = {
    start: () => {
        (0,routing_controllers__WEBPACK_IMPORTED_MODULE_3__.useContainer)(typedi__WEBPACK_IMPORTED_MODULE_4__.Container);
        const app = express__WEBPACK_IMPORTED_MODULE_2___default()();
        app.use(function (req, res, next) {
            req.setTimeout(0); // no timeout for all requests, your server will be DoS'd
            next();
        });
        app.use(express__WEBPACK_IMPORTED_MODULE_2___default().urlencoded({ extended: true }));
        app.use(express__WEBPACK_IMPORTED_MODULE_2___default().json());
        // Security Configuration
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET environment variable is required for production security');
        }
        // Authentication middleware chain (production-ready security)
        app.use('/api', _middleware_authMiddleware__WEBPACK_IMPORTED_MODULE_9__.withUserAuthentication);
        app.use('/api', _middleware_authMiddleware__WEBPACK_IMPORTED_MODULE_9__.withTenant);
        // Security enforcement middleware - REQUIRES VALID AUTHENTICATION
        app.use('/api', _middleware_authMiddleware__WEBPACK_IMPORTED_MODULE_9__.requireAuth);
        app.use('/api', _middleware_authMiddleware__WEBPACK_IMPORTED_MODULE_9__.requireTenant);
        // Apply microservice context middleware after tenant resolution
        app.use('/api', _webaverse_middleware_contextMiddleware__WEBPACK_IMPORTED_MODULE_8__.contextMiddleware);
        // Configure controllers
        (0,routing_controllers__WEBPACK_IMPORTED_MODULE_3__.useExpressServer)(app, {
            routePrefix: '/api',
            controllers: [
                _webaverse_controllers_hello_controller__WEBPACK_IMPORTED_MODULE_5__.HelloController,
                _webaverse_controllers_eContentContent_controller__WEBPACK_IMPORTED_MODULE_6__.EContentContentController,
                _webaverse_controllers_files_controller__WEBPACK_IMPORTED_MODULE_7__.FilesController
            ],
        });
        app.get('*', (req, res, next) => {
            if (req.path.startsWith('/api')) {
                next();
            }
            else {
                res.end();
            }
        });
        const server = app.listen(process.env.PORT || 3000, () => {
            // eslint-disable-next-line no-console
            console.log(`⚡️[server]: Server is running at https://localhost:${process.env.PORT || 3000}`);
        });
        server.setTimeout(0);
    },
};
Search.start();

})();

/******/ })()
;
//# sourceMappingURL=api-backend.js.map