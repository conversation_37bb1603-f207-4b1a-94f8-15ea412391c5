"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function getModelCommon(Ids) {
    const Values = Object.keys(Ids).reduce((hash, value) => {
        hash[Ids[value]] = value;
        return hash;
    }, {});
    return {
        serialize: value => Ids[value],
        deserialize: id => Values[id],
        valueById: id => Values[id],
        idByValue: value => Ids[value],
    };
}
exports.default = getModelCommon;
//# sourceMappingURL=getModelCommon.js.map