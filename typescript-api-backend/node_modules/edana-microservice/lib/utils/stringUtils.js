"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.beginWithUnderline = exports.wrapIntoRoundBrackets = exports.wrapIntoDoubleQuotes = exports.wrapIntoSingleQuotes = exports.firstCharToLowerCase = exports.firstCharToUpperCase = void 0;
function firstCharToUpperCase(str) {
    return str.charAt(0).toUpperCase() + str.substring(1);
}
exports.firstCharToUpperCase = firstCharToUpperCase;
function firstCharToLowerCase(str) {
    return str.charAt(0).toLowerCase() + str.substring(1);
}
exports.firstCharToLowerCase = firstCharToLowerCase;
function wrapIntoSingleQuotes(str) {
    return `'${str}'`;
}
exports.wrapIntoSingleQuotes = wrapIntoSingleQuotes;
function wrapIntoDoubleQuotes(str) {
    return `"${str}"`;
}
exports.wrapIntoDoubleQuotes = wrapIntoDoubleQuotes;
function wrapIntoRoundBrackets(str) {
    return `(${str})`;
}
exports.wrapIntoRoundBrackets = wrapIntoRoundBrackets;
function beginWithUnderline(str) {
    for (let i = 0; i < str.length; i++) {
        const char = str.charAt(i);
        if (char === char.toUpperCase() && i !== 0) {
            str = `${str.slice(0, i)}_${str.slice(i)}`;
            i++;
        }
    }
    return str;
}
exports.beginWithUnderline = beginWithUnderline;
//# sourceMappingURL=stringUtils.js.map