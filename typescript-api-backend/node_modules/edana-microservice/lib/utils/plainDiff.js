"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function plainDiff(originalArray, nextArray) {
    const all = new Set(originalArray);
    const deleted = new Set(originalArray);
    const added = new Set();
    for (const item of nextArray) {
        if (all.has(item)) {
            deleted.delete(item);
        }
        else {
            added.add(item);
        }
    }
    return { deleted, added };
}
exports.default = plainDiff;
//# sourceMappingURL=plainDiff.js.map