"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HumanError = exports.DateTime = exports.ContextBuilder = exports.DeleteResponse = exports.utils = exports.withRedisConnectionFactory = exports.withConnection = exports.sqlHelpers = exports.migrate = exports.validators = exports.ORACLE_TYPES = exports.EntityNoId = exports.EntityTree = exports.Entity = exports.log = exports.rabbitmqClient = exports.getOracleErrorCode = exports.startMicroservice = exports.ResultSet = exports.BindParameters = exports.errors = exports.actionHelpers = void 0;
const dotenv_1 = require("dotenv");
const luxon_1 = require("luxon");
Object.defineProperty(exports, "DateTime", { enumerable: true, get: function () { return luxon_1.DateTime; } });
const oracledb_1 = require("oracledb");
Object.defineProperty(exports, "BindParameters", { enumerable: true, get: function () { return oracledb_1.BindParameters; } });
Object.defineProperty(exports, "ResultSet", { enumerable: true, get: function () { return oracledb_1.ResultSet; } });
const startMicroservice_1 = __importDefault(require("./startMicroservice"));
exports.startMicroservice = startMicroservice_1.default;
const utils_1 = __importDefault(require("./utils"));
exports.utils = utils_1.default;
const Entity_1 = __importDefault(require("./frameworks/Entity"));
exports.Entity = Entity_1.default;
const log_1 = require("./support/log");
Object.defineProperty(exports, "log", { enumerable: true, get: function () { return log_1.coreLogger; } });
const rabbitmq_client_1 = require("./support/rabbitmq_client");
Object.defineProperty(exports, "rabbitmqClient", { enumerable: true, get: function () { return rabbitmq_client_1.client; } });
const errorUtils_1 = require("./support/errorUtils");
Object.defineProperty(exports, "getOracleErrorCode", { enumerable: true, get: function () { return errorUtils_1.getOracleErrorCode; } });
const StatusCodes_1 = __importDefault(require("./frameworks/HumanErrors/StatusCodes"));
const cookWhereClauses_1 = __importDefault(require("./frameworks/Entity/actions/common/cookWhereClauses"));
const cookOracleCatSearchQuery_1 = __importDefault(require("./frameworks/Entity/actions/common/cookOracleCatSearchQuery"));
const cookCursorData_1 = __importDefault(require("./frameworks/Entity/actions/common/cookCursorData"));
const oracleTypes_1 = require("./support/oracleTypes");
Object.defineProperty(exports, "ORACLE_TYPES", { enumerable: true, get: function () { return oracleTypes_1.ORACLE_TYPES; } });
const HumanErrors_1 = require("./frameworks/HumanErrors");
Object.defineProperty(exports, "HumanError", { enumerable: true, get: function () { return HumanErrors_1.HumanError; } });
const migration_1 = __importDefault(require("./support/migration"));
exports.migrate = migration_1.default;
const validators_1 = __importDefault(require("./frameworks/Entity/validators"));
exports.validators = validators_1.default;
const oracleDatabasePool_1 = require("./support/database/oracleDatabasePool");
Object.defineProperty(exports, "withConnection", { enumerable: true, get: function () { return oracleDatabasePool_1.withConnection; } });
const redisConnectionFactory_1 = __importDefault(require("./support/database/redisConnectionFactory"));
exports.withRedisConnectionFactory = redisConnectionFactory_1.default;
const sqlHelpers_1 = __importDefault(require("./support/sqlHelpers"));
exports.sqlHelpers = sqlHelpers_1.default;
const DeleteResponse_1 = __importDefault(require("./frameworks/Entity/DeleteResponse"));
exports.DeleteResponse = DeleteResponse_1.default;
const ContextBuilder_1 = __importDefault(require("./startMicroservice/ContextBuilder"));
exports.ContextBuilder = ContextBuilder_1.default;
const EntityTree_1 = __importDefault(require("./frameworks/Entity/EntityTree"));
exports.EntityTree = EntityTree_1.default;
const EntityNoId_1 = __importDefault(require("./frameworks/Entity/EntityNoId"));
exports.EntityNoId = EntityNoId_1.default;
const PRODUCTION = process.env.NODE_ENV === 'production';
const DEVELOPMENT = !PRODUCTION;
if (DEVELOPMENT) {
    (0, dotenv_1.config)();
}
if (process.env.IGNORE_ORACLE) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    require.cache[require.resolve('oracledb')] = { exports: {} };
}
exports.actionHelpers = {
    cookWhereClauses: cookWhereClauses_1.default,
    cookOracleCatSearchQuery: cookOracleCatSearchQuery_1.default,
    cookCursorData: cookCursorData_1.default,
};
exports.errors = {
    ORACLE_ERRORS: errorUtils_1.ORACLE_ERRORS,
    getOracleErrorCode: errorUtils_1.getOracleErrorCode,
    statusCodes: StatusCodes_1.default,
    HumanError: HumanErrors_1.HumanError,
    InvalidRequestError: HumanErrors_1.InvalidRequestError,
    NotAuthenticatedError: HumanErrors_1.NotAuthenticatedError,
    NotAuthorizedError: HumanErrors_1.NotAuthorizedError,
    NotFoundError: HumanErrors_1.NotFoundError,
    InternalServerError: HumanErrors_1.InternalServerError,
    NotImplementedError: HumanErrors_1.NotImplementedError,
    NotUniqueError: HumanErrors_1.NotUniqueError,
    InvalidSocialCredentialsError: HumanErrors_1.InvalidSocialCredentialsError,
    CredentialsExpiredError: HumanErrors_1.CredentialsExpiredError,
    InactiveAllocationsError: HumanErrors_1.InactiveAllocationsError,
    InvalidLoginError: HumanErrors_1.InvalidLoginError,
    InvalidCredentialsError: HumanErrors_1.InvalidCredentialsError,
    MaxLoginTriesError: HumanErrors_1.MaxLoginTriesError,
    ConfirmationTokenInvalidError: HumanErrors_1.ConfirmationTokenInvalidError,
    NoCellToNotifyError: HumanErrors_1.NoCellToNotifyError,
    NoEmailToNotifyError: HumanErrors_1.NoEmailToNotifyError,
};
exports.default = {
    startMicroservice: startMicroservice_1.default,
    getOracleErrorCode: errorUtils_1.getOracleErrorCode,
    rabbitmqClient: rabbitmq_client_1.client,
    log: log_1.coreLogger,
    Entity: Entity_1.default,
    EntityTree: EntityTree_1.default,
    EntityNoId: EntityNoId_1.default,
    ORACLE_TYPES: oracleTypes_1.ORACLE_TYPES,
    validators: validators_1.default,
    migrate: migration_1.default,
    utils: utils_1.default,
    actionHelpers: exports.actionHelpers,
    errors: exports.errors,
    HumanError: HumanErrors_1.HumanError,
    DeleteResponse: DeleteResponse_1.default,
};
//# sourceMappingURL=index.js.map