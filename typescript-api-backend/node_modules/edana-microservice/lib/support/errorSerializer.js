"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    serialize: serializeError,
    deserialize: deserializeError,
};
function serializeError(error) {
    return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        status: error.status,
        statusCode: error.statusCode,
        rawErrors: error.rawErrors || (error.results && error.results.errors),
        humanErrors: error.humanErrors,
        path: error.path,
    };
}
function deserializeError(responseError) {
    return new DeserializedError(responseError);
}
class DeserializedError extends Error {
    constructor(jsonError) {
        super(jsonError.message);
        const { name, stack, status, statusCode, rawErrors, humanErrors, } = jsonError;
        Error.captureStackTrace(this, DeserializedError);
        this.name = name;
        this.stack = stack ? stack.join('\n') : null;
        this.status = status;
        this.statusCode = statusCode;
        this.rawErrors = rawErrors;
        this.humanErrors = humanErrors;
    }
}
//# sourceMappingURL=errorSerializer.js.map