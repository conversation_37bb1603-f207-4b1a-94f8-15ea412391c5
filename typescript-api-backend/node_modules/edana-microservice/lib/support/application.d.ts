export declare function initializeLayer<T>(layerId: string, { initialize, tearDown }: IInitializeLayerOptions<T>): (() => Promise<void>) | ((callback?: CallbackType<T>) => Promise<CallbackType<T>>);
type CallbackType<T> = (layerObject?: T) => Promise<void>;
export interface IInitializeLayerOptions<T> {
    initialize: () => Promise<T>;
    tearDown: (layerObject: T) => Promise<void>;
}
export interface ILayerValue<T> extends IInitializeLayerOptions<T> {
    promise: Promise<T> | null;
    layerId: string;
    activeCallbacks: Map<string, Promise<T>>;
}
export {};
