"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const getParentPackage_1 = __importDefault(require("../getParentPackage"));
const discoverMicroservice_1 = __importDefault(require("./discoverMicroservice"));
function makeServiceRegistry(basePath) {
    const parent = (0, getParentPackage_1.default)(basePath);
    const dependencies = parent.serviceDependencies || {};
    const registry = {};
    return (serviceName) => __awaiter(this, void 0, void 0, function* () {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        //@ts-ignore
        if (registry[serviceName]) {
            return yield registry[serviceName];
        }
        return yield loadService(serviceName);
    });
    function loadService(serviceName) {
        return __awaiter(this, void 0, void 0, function* () {
            const version = dependencies[serviceName];
            if (!version) {
                throw new Error(`service registry: service [${serviceName}] is not listed in serviceDependencies`);
            }
            registry[serviceName] = (0, discoverMicroservice_1.default)(serviceName, version);
            try {
                return yield registry[serviceName];
            }
            catch (e) {
                delete registry[serviceName];
                throw e;
            }
        });
    }
}
exports.default = makeServiceRegistry;
//# sourceMappingURL=makeServiceRegistry.js.map