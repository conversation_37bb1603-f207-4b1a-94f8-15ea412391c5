"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const semver_1 = __importDefault(require("semver"));
function validateServiceVersion(serviceInfo, targetVersion) {
    const { name, url, version } = serviceInfo;
    if (!semver_1.default.satisfies(version, targetVersion)) {
        // eslint-disable-next-line no-console
        console.warn(`error discovering service [${name}]: discovered instance at ${url} has version ${version} which does not satisfy required "${targetVersion}"`);
    }
}
exports.default = validateServiceVersion;
//# sourceMappingURL=validateServiceVersion.js.map