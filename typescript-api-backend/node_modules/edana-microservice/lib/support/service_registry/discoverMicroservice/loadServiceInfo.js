"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const url_1 = __importDefault(require("url"));
const swagger_client_1 = __importDefault(require("swagger-client"));
const semver_1 = __importDefault(require("semver"));
const constants_1 = require("../../../constants");
function loadServiceInfo(serviceName, serviceUrl) {
    return __awaiter(this, void 0, void 0, function* () {
        const specUrl = url_1.default.resolve(serviceUrl, constants_1.OPENAPI_SPEC_PATH);
        const swaggerClient = yield (0, swagger_client_1.default)(specUrl);
        const spec = swaggerClient.spec;
        if (!spec || !spec.info) {
            throw new Error(`invalid [${serviceName}] service spec at ${specUrl}: missing info section`);
        }
        const { title, version } = spec.info;
        if (!title || !version) {
            throw new Error(`invalid [${serviceName}] service spec at ${specUrl}: info section is missing title or version`);
        }
        if (title !== serviceName) {
            throw new Error(`invalid [${serviceName}] service spec at ${specUrl}: expected title "${serviceName}" but got "${title}"`);
        }
        if (!semver_1.default.valid(version)) {
            throw new Error(`invalid [${serviceName}] service spec at ${specUrl}: invalid semver "${version}"`);
        }
        return {
            name: title,
            version,
            url: serviceUrl,
            spec,
            swaggerClient,
        };
    });
}
exports.default = loadServiceInfo;
//# sourceMappingURL=loadServiceInfo.js.map