"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function findServiceUrl(serviceName) {
    const varName = urlEnvVariableName(serviceName);
    const value = process.env[varName];
    if (!value) {
        throw new Error(`error discovering microservice ${serviceName}: env variable ${varName} must be present`);
    }
    return value;
}
exports.default = findServiceUrl;
function urlEnvVariableName(serviceName) {
    const cookedServiceName = serviceName
        .toUpperCase()
        .replace(/[^\w\d]+/gi, '_')
        .replace(/_+/g, '_');
    return `SERVICE_URL_${cookedServiceName}`;
}
//# sourceMappingURL=findServiceUrl.js.map