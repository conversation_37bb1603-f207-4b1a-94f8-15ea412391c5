declare const _default: {
    serialize: typeof serializeError;
    deserialize: typeof deserializeError;
};
export default _default;
declare function serializeError(error: any): {
    name: any;
    message: any;
    stack: any;
    status: any;
    statusCode: any;
    rawErrors: any;
    humanErrors: any;
    path: any;
};
declare function deserializeError(responseError: any): DeserializedError;
declare class DeserializedError extends Error {
    status: string;
    statusCode: number;
    rawErrors: string[];
    humanErrors: string[];
    constructor(jsonError: any);
}
