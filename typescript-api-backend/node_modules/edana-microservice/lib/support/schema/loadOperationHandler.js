"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const errorSerializer_1 = __importDefault(require("../../support/errorSerializer"));
function loadOperationHandler(serviceMeta, operation) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        const { endpointsBasePath } = serviceMeta;
        const { tags, operationId } = operation;
        const [tag] = tags;
        const operationPath = path_1.default.normalize(path_1.default.join(endpointsBasePath, 'endpoints', tag, operationId));
        const operationHandlerPromise = yield (_a = operationPath, Promise.resolve().then(() => __importStar(require(_a))));
        return handler2koa(operationHandlerPromise);
    });
}
exports.default = loadOperationHandler;
function handler2koa({ default: operationHandler }) {
    return (ctx, next) => __awaiter(this, void 0, void 0, function* () {
        try {
            const result = yield operationHandler(Object.assign(Object.assign({ ctx }, ctx.state), ctx.state.requestFramework), ctx.req.params, next);
            if (!ctx.body) {
                ctx.status = 200;
                ctx.body = result;
            }
        }
        catch (error) {
            const { humanErrorRegistry } = ctx.state;
            error.humanErrors = humanErrorRegistry.extractHumanErrors(error);
            ctx.status = error.statusCode || 500;
            ctx.body = errorSerializer_1.default.serialize(error);
        }
    });
}
//# sourceMappingURL=loadOperationHandler.js.map