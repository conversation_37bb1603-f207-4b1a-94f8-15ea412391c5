"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const camelcase_1 = __importDefault(require("camelcase"));
const constants_1 = require("../../constants");
function validateSpec(spec) {
    validateTagsPresentAndCamelCase(spec);
    validateOperationIdsPresentAndCamelCase(spec);
    validateOperationIdsUnique(spec);
    validateSecurityDefinitionsCorrect(spec);
}
exports.default = validateSpec;
function validateSecurityDefinitionsCorrect(spec) {
    for (const [path, operations] of Object.entries(spec.paths)) {
        for (const [operationName, operation] of Object.entries(operations)) {
            if (operation.security) {
                if (operation.security.length !== 1) {
                    throw new Error(`swagger spec: ${operationName} ${path}: exactly one security must be specified`);
                }
                if (Object.keys(operation.security[0]).length !== 1 ||
                    !operation.security[0][constants_1.TOKEN_AUTH_SECURITY_DEFINITION_NAME]) {
                    throw new Error(`swagger spec: ${operationName} ${path}: only ${constants_1.TOKEN_AUTH_SECURITY_DEFINITION_NAME} security definition is supported`);
                }
            }
        }
    }
}
function validateTagsPresentAndCamelCase(spec) {
    for (const [path, operations] of Object.entries(spec.paths)) {
        for (const [operationName, operation] of Object.entries(operations)) {
            if (!operation.tags || operation.tags.length !== 1) {
                throw new Error(`swagger spec:  ${operationName} ${path}: exactly one tag should be specified`);
            }
            if (!isCamelCase(operation.tags[0])) {
                throw new Error(`swagger spec: ${operationName} ${path}: tag should be present and be formatted in camel-case`);
            }
        }
    }
}
function validateOperationIdsPresentAndCamelCase(spec) {
    for (const [path, operations] of Object.entries(spec.paths)) {
        for (const [operationName, operation] of Object.entries(operations)) {
            if (!operation.operationId) {
                throw new Error(`swagger spec: ${operationName} ${path}: operationId should be specified`);
            }
            if (!isCamelCase(operation.operationId)) {
                throw new Error(`swagger spec: ${operationName} ${path}: operationId ${operation.operationId} should be formatted in camel-case`);
            }
        }
    }
}
function validateOperationIdsUnique(spec) {
    const set = new Set();
    for (const operations of Object.values(spec.paths)) {
        for (const { operationId } of Object.values(operations)) {
            if (set.has(operationId)) {
                throw new Error(`swagger spec: operation id ${operationId} is not unique`);
            }
            set.add(operationId);
        }
    }
}
function isCamelCase(s) {
    return typeof s === 'string' && !!s && (0, camelcase_1.default)(s) === s;
}
//# sourceMappingURL=ISpec.js.map