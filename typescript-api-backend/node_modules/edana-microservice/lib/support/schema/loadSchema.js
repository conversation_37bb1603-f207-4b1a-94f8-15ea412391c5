"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const enhanceSpec_1 = __importDefault(require("./enhanceSpec"));
const ISpec_1 = __importDefault(require("./ISpec"));
const constants_1 = require("../../constants");
const log_1 = require("../log");
function loadSchema(serviceMeta, hasReports) {
    const { name, version, endpointsBasePath } = serviceMeta;
    const miroserviceEndpoints = [
        `${endpointsBasePath}/**/*.js`,
        `${endpointsBasePath}/**/*.ts`,
        `${endpointsBasePath}/**/*.yaml`,
    ];
    const reportsEndpoints = [
        `${constants_1.ENDPOINTS_PATH}/**/*.js`,
        `${constants_1.ENDPOINTS_PATH}/**/*.ts`,
        `${constants_1.ENDPOINTS_PATH}/**/*.yaml`,
    ];
    try {
        const spec = (0, swagger_jsdoc_1.default)({
            swaggerDefinition: {
                info: {
                    title: name,
                    version,
                },
                securityDefinitions: {
                    [constants_1.TOKEN_AUTH_SECURITY_DEFINITION_NAME]: {
                        type: 'apiKey',
                        in: 'header',
                        name: 'Authorization',
                    },
                },
            },
            apis: hasReports
                ? miroserviceEndpoints.concat(reportsEndpoints)
                : miroserviceEndpoints,
        });
        (0, enhanceSpec_1.default)(spec);
        (0, ISpec_1.default)(spec);
        log_1.coreLogger.silly(`...loaded microservice schema: \n${JSON.stringify(spec, null, 2)}`);
        return spec;
    }
    catch (e) {
        log_1.coreLogger.error(`'error loading microservice schema from ${endpointsBasePath}: '`, e);
        throw new Error('microservice schema loading failed');
    }
}
exports.default = loadSchema;
//# sourceMappingURL=loadSchema.js.map