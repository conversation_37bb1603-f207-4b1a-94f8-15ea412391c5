"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeLayer = void 0;
const uuid_1 = __importDefault(require("uuid"));
const log_1 = require("./log");
const State = {
    shuttingDown: false,
    layers: new Map(),
};
setUpGracefulShutdown();
function initializeLayer(layerId, { initialize, tearDown }) {
    if (State.shuttingDown) {
        return deny;
    }
    if (State.layers.has(layerId)) {
        throw new Error(`layer ${layerId} has already been initialized`);
    }
    State.layers.set(layerId, {
        layerId,
        promise: null,
        initialize,
        tearDown,
        activeCallbacks: new Map(),
    });
    return (callback) => acquireLayer(layerId, callback);
}
exports.initializeLayer = initializeLayer;
function acquireLayer(layerId, callback) {
    return __awaiter(this, void 0, void 0, function* () {
        if (State.shuttingDown) {
            return deny();
        }
        const layer = State.layers.get(layerId);
        if (!layer) {
            return;
        }
        if (layer.promise && callback) {
            return callback(yield layer.promise);
        }
        const { initialize } = layer;
        layer.promise = initialize();
        try {
            const layerObject = yield layer.promise;
            if (!callback) {
                return layerObject;
            }
            return invokeCallback(layerId, callback, layerObject);
        }
        catch (error) {
            log_1.coreLogger.error(`error acquiring item from ${layerId}: `, error);
            layer.promise = null;
            throw error;
        }
    });
}
function deny() {
    return __awaiter(this, void 0, void 0, function* () {
        throw new Error('application is shutting down');
    });
}
function setUpGracefulShutdown() {
    process.on('SIGINT', () => shutDown('SIGINT'));
}
function invokeCallback(layerId, callback, layerObject) {
    return __awaiter(this, void 0, void 0, function* () {
        const id = uuid_1.default.v4();
        const layer = State.layers.get(layerId);
        if (!layer) {
            return;
        }
        try {
            const promise = callback(layerObject);
            layer.activeCallbacks.set(id, promise);
            return yield promise;
        }
        finally {
            layer.activeCallbacks.delete(id);
        }
    });
}
function shutDown(signal) {
    return __awaiter(this, void 0, void 0, function* () {
        if (State.shuttingDown) {
            return;
        }
        log_1.coreLogger.info(`${signal}: shutting down now...`);
        State.shuttingDown = true;
        const values = State.layers.values();
        for (const { promise, tearDown, layerId, activeCallbacks } of values) {
            log_1.coreLogger.info(`shutting down ${layerId}...`);
            if (!promise) {
                log_1.coreLogger.info(`${layerId} was not initialized, no shutdown needed`);
                continue;
            }
            const activeCallbackPromises = Array.from(activeCallbacks.values());
            if (activeCallbackPromises.length) {
                try {
                    log_1.coreLogger.info(`${layerId}: awaiting ${activeCallbackPromises.length} operations in progress...`);
                    yield Promise.all(activeCallbackPromises);
                    log_1.coreLogger.info(`${layerId}: all pending operations completed`);
                }
                catch (error) {
                    log_1.coreLogger.error(`${layerId}: some pending operations failed: `, error);
                }
            }
            try {
                const layerObject = yield promise;
                try {
                    yield tearDown(layerObject);
                    log_1.coreLogger.info(`${layerId} shut down gracefully`);
                }
                catch (tearDownError) {
                    log_1.coreLogger.error(`${layerId} shut down failed: `, tearDownError);
                }
            }
            catch (initError) {
                log_1.coreLogger.info(`${layerId} initialization failed before, no shutdown needed`);
            }
        }
        // eslint-disable-next-line no-process-exit
        process.exit();
    });
}
//# sourceMappingURL=application.js.map