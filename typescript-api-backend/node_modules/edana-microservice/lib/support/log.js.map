{"version": 3, "file": "log.js", "sourceRoot": "", "sources": ["../../src/support/log.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAAkD;AAElD,wEAAgD;AAChD,qCAAqC;AAGrC,MAAM,UAAU,GAAG;IACjB,IAAI,iBAAO,CAAC,UAAU,CAAC,OAAO,CAAC;QAC7B,QAAQ,EAAE,CAAC,CAAC,CAAC,kBAAS,KAAK,OAAO,IAAI,kBAAS,KAAK,OAAO,CAAC;QAC5D,SAAS,EAAE,IAAI;QACf,WAAW,EAAE,CAAC,CAAC,CAAC,kBAAS,KAAK,OAAO,IAAI,kBAAS,KAAK,OAAO,CAAC;QAC/D,IAAI,EAAE,CAAC,CAAC,kBAAS,KAAK,OAAO,IAAI,kBAAS,KAAK,OAAO,CAAC;QACvD,SAAS,EAAE,CAAC,CAAC,kBAAS,KAAK,OAAO,IAAI,kBAAS,KAAK,OAAO,CAAC;QAC5D,KAAK,EAAE,kBAAS;QAChB,gBAAgB,EAAE,IAAI;QACtB,+BAA+B,EAAE,KAAK;KACvC,CAAC;CACH,CAAC;AAEW,QAAA,UAAU,GAAG,IAAI,iBAAO,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,CAAC,CAAC;AAE7D,kBAAe;IACb,iBAAiB;CAClB,CAAC;AAEF,SAAgB,iBAAiB,CAAC,UAAiB;IACjD,OAAO,IAAI,iBAAO,CAAC,MAAM,CAAC;QACxB,UAAU;QACV,SAAS,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;KACxC,CAAC,CAAC;AACL,CAAC;AALD,8CAKC;AAED,SAAS,cAAc,CAAC,UAAiB;IACvC,MAAM,EACJ,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,EACxB,MAAM,EAAE,aAAa,EACrB,GAAG,EAAE,UAAU,GAChB,GAAG,UAAU,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAEhC,OAAO,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,EAAE;QAC/B,IAAI,IAAI,GAAG,SAAS,CAAC;QAErB,IAAI,IAAI,YAAY,KAAK,EAAE;YACzB,IAAI,GAAG;gBACL,KAAK,EAAE,yBAAe,CAAC,SAAS,CAAC,IAAI,CAAC;aACvC,CAAC;SACH;aAAM,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,YAAY,KAAK,EAAE;YACpD,IAAI,CAAC,KAAK,GAAG,yBAAe,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACpD;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACvD,IAAI,aAAa,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;SACxC;QACD,IAAI,UAAU,EAAE;YACd,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,CAAC,CAAC;SACrC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;AACJ,CAAC"}