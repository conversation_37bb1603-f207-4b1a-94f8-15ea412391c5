"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const cookQuery_1 = __importDefault(require("./cookQuery"));
const stringCleanUp_1 = __importDefault(require("./stringCleanUp"));
exports.default = {
    cookQuery: cookQuery_1.default,
    stringCleanUp: stringCleanUp_1.default,
};
//# sourceMappingURL=index.js.map