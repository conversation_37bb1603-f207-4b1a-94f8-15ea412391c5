"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function cookQuery({ fields, table, where, order, groupBy, }) {
    return [
        cookFieldsStatement(fields),
        cookFromStatement(table),
        cookWhereStatement(where),
        cookOrderStatement(order),
        cookGroupStatement(groupBy),
    ]
        .filter(s => !!s)
        .join('\n');
}
exports.default = cookQuery;
function cookFieldsStatement(fields) {
    if (typeof fields === 'string') {
        return withSelect([fields]);
    }
    else if (Array.isArray(fields)) {
        return withSelect(fields);
    }
    else {
        throw new Error(`cookFieldsStatement: Invalid type ${typeof fields}`);
    }
}
function cookFromStatement(table) {
    if (table) {
        return withFrom(table);
    }
    else {
        throw new Error('Error at cookFromStatement: Table name is not provided');
    }
}
/*
where: 'NAME = UserName'
where: {'NAME = UserName': true}
where: ['NAME = UserName', 'LAST_NAME = lastname']
*/
function cookWhereStatement(where) {
    if (typeof where === 'string') {
        return withWhere([where]);
    }
    else if (Array.isArray(where)) {
        return withWhere(where);
    }
    else if (typeof where === 'object') {
        const statments = Object.keys(where)
            .map(key => where[key] && key)
            .filter(item => item);
        return withWhere(statments);
    }
    else if (where) {
        throw new Error(`cookWhereStatement: Invalid type ${typeof where}`);
    }
    return '';
}
function cookOrderStatement(order) {
    return order ? withOrder(order) : '';
}
function cookGroupStatement(groupBy) {
    return groupBy ? withGroup(groupBy) : '';
}
function withSelect(statmentsArray) {
    return `SELECT ${statmentsArray.join(', ')}`;
}
function withFrom(tableName) {
    return `FROM ${tableName}`;
}
function withWhere(statmentsArray) {
    if (!statmentsArray.length) {
        return '';
    }
    return `WHERE ${statmentsArray.join(' AND ')}`;
}
function withOrder({ key, reversed }) {
    return `ORDER BY ${key} ${!reversed ? 'ASC' : 'DESC'}`;
}
function withGroup(key) {
    return `GROUP BY ${key}`;
}
//# sourceMappingURL=cookQuery.js.map