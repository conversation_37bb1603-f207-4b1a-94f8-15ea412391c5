"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function measure(logger, defaultLevel = 'info') {
    const start = time();
    return (error, message, inputMeta) => {
        const level = error ? 'error' : defaultLevel;
        const duration = (time() - start).toFixed(0);
        const meta = Object.assign(Object.assign({}, inputMeta), { duration: `${duration}ms` });
        if (error) {
            meta.error = error;
        }
        if (logger) {
            logger[level](message, meta);
        }
        return duration;
    };
}
exports.default = measure;
function time() {
    const [sec, nanosec] = process.hrtime();
    return sec * 1000 + nanosec / 1000000;
}
//# sourceMappingURL=measure.js.map