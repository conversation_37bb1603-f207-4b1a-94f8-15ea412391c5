import { Redis } from 'ioredis';
import { ParameterizedContext } from 'koa';
import { Field } from '../frameworks/Entity/makeFields';
import { IConnection } from './database/oracleDatabasePool';
import { LoggerInstance } from 'winston';
import { ErrorParamsType } from '../frameworks/HumanErrors/NotFoundError';
import { HumanError, InternalServerError, InvalidRequestError, NotAuthenticatedError, NotAuthorizedError, NotFoundError, NotImplementedError, NotUniqueError, ServiceUnavailableError } from '../frameworks/HumanErrors';
import { IWithType } from '../frameworks/Entity';
import { ICache } from './cache/interfaces';
import { TFsClient } from './service_client/IFileService';
export interface CustomOptions<T extends IWithType> {
    tableName?: string;
    idSequenceName?: string;
    handleActionError?: (actionName: string, error: unknown, framework: IFramework<T>, ...args: any[]) => void;
    skipDefaultActions?: boolean;
    cache?: TCache;
}
export type TCache = ICacheOptions | ICacheOptions[] | null;
export interface ICacheOptions {
    key: string;
    list: boolean;
    loader: (connection: any, args?: any) => Promise<any>;
}
export interface IFramework<T extends IWithType> {
    fields: Field[];
    fieldsByName: {
        [key: string]: Field;
    };
    tableName: string;
    entityName: string;
    createInstance: (arg: Record<string, any>) => T;
    extractInstanceFromOutBinds: (outBounds?: Record<string, any>, sourceValues?: Partial<T>) => T;
    makeRetData: () => {
        queryClause: string;
        retParams: Record<string, string>;
        shortQueryClause: string;
    };
    serializeQueryParams: (params: any, fields: Field[]) => any;
}
export interface IFrameworkTree<T extends IWithType> extends IFramework<T> {
    treeParentIdField: string | null;
}
export interface Validators<T extends IWithType> {
    validators?: {
        actions: string[];
        validators: {
            validate: (...any: any[]) => void;
            params: (entity: Omit<T, keyof IWithType>) => any;
            errorMessage?: string;
            skip?: (entity: Omit<T, keyof IWithType>) => boolean;
            [key: string]: any;
        }[];
    }[];
}
export interface IContext {
    connection: IConnection;
    cache: ICache;
    tenantId: number;
    isSuperTenant: boolean;
    organisationGroupId?: number;
    organisationsIds?: Array<number>;
    userId: number;
    userName: string;
    log: LoggerInstance;
    redisConnectionFactory: IRedisConnectionFactory | null;
    fsClient: TFsClient;
    requireServiceClient: (name: string) => Promise<{
        [key: string]: {
            [key: string]: (...args: any[]) => Promise<any>;
        };
    }>;
    raiseHumanError: (errors: ErrorParamsType) => HumanError;
    raiseInvalidRequestError: (description: string) => InvalidRequestError;
    raiseNotAuthenticatedError: () => NotAuthenticatedError;
    raiseNotAuthorizedError: () => NotAuthorizedError;
    raiseNotFoundError: (entity: {
        name: string;
    }) => NotFoundError;
    raiseInternalServerError: () => InternalServerError;
    raiseNotImplementedError: () => NotImplementedError;
    raiseServiceUnavailableError: () => ServiceUnavailableError;
    raiseNotUniqueError: () => NotUniqueError;
    ctx: ICtx;
}
export type ICtx = ParameterizedContext<{
    id: string;
    log: LoggerInstance;
    googleUser?: {
        googleUsername: string;
    };
    microsoftUser?: {
        microsoftUsername: string;
    };
    userId?: number;
    tenantId: number;
    organisationGroupId?: number;
    organisationsIds?: Array<number>;
    userName?: string;
    accessHash?: string;
    token?: string;
}, {
    isSubstitution?: boolean;
}>;
export type TInputFields<TEntity> = {
    [Key in keyof Omit<TEntity, keyof IWithType>]?: TInputFieldPreset | IInputField<TEntity[Key], TEntity> | TInputFieldSwitch<TEntity[Key], TEntity>;
};
export type TInputFieldSwitch<TSingle, TEntity> = TSingle extends string ? IInputField<TSingle, TEntity> & {
    mapping?: Record<TSingle, string | number>;
} : IInputField<TSingle, TEntity>;
export interface IInputField<TSingle, TEntity> {
    preset?: TInputFieldPreset;
    columnType?: number;
    insertOverride?: ((params: Partial<TEntity>) => string | TSingle) | TSingle | string;
    canUpdate?: boolean;
    serialize?: (value: TSingle) => string | number | null;
    deserialize?: (id: string | number | null) => TSingle;
    columnName?: string;
}
export type TInputFieldPreset = 'string' | 'number' | 'date' | 'status' | 'boolean' | 'datetime' | 'json';
export interface IRedisConnectionFactory {
    createClient: (clientId: string) => Redis;
    getClient: (clientId: string) => Redis;
    releaseByClientId: (clientId: string) => void;
    release: (client: Redis) => void;
}
