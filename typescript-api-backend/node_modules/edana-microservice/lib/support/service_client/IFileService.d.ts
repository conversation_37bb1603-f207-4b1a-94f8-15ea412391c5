import { DateTime } from 'luxon';
interface IFileCategory {
    id: number;
    key: string;
    name: string;
    fileCategoryGroupId: number | null;
}
interface ICategoryArea {
    id: number;
    fileCategoryId: number;
    key: string;
    fileCategoryKey: string;
    requiresOrganisationGroupId: boolean;
}
interface ICategoryGroup {
    id: number;
    parentId: number | null;
    name: string;
}
interface IStorageInstance {
    id: number;
    fileStorageSettingId: number;
    folderPath: string;
    quota: number | null;
    maxFileSize: number | null;
    periodKey: string;
}
export type TFsClient = Promise<{
    addEntityAttachments: (fileCategoryArea: string, referenceId: number | string, attachmentInputs: IAttachFileInfo[]) => Promise<{
        id: number;
    }[]>;
    getFileCategoryInfo: () => Promise<{
        fileCategories: IFileCategory[];
        categoryArea: ICategoryArea[];
        categoryGroups: ICategoryGroup[];
    }>;
    getStorageInstances: ({ categoryKey, tenantId, organisationGroupId, }: {
        categoryKey?: string;
        tenantId: number;
        organisationGroupId?: number;
    }) => Promise<IStorageInstance[]>;
    initiateAttachmentUpload: (fileCategoryAreaKey: string, files: INewUploadFile[]) => Promise<INewUploadFileResponse[]>;
    loadEntityAttachmentByFileId: (fileId: number) => Promise<IFileMetadata>;
    loadEntityAttachments: (fileCategoryArea: string, referenceId: string | number, { includeAll }?: {
        includeAll?: boolean;
    }) => Promise<IFileAttachment[]>;
    bulkLoadEntityAttachments: (fileCategoryArea: string, referenceIds: string[] | number[], { first, count, includeAll, }?: {
        first?: number;
        count?: number;
        includeAll?: boolean;
    }) => Promise<IFileAttachment[]>;
    bulkLoadEntityAttachmentsCount: (fileCategoryArea: string, referenceIds: string[] | number[], { includeAll }?: {
        includeAll?: boolean;
    }) => Promise<number>;
    updateEntityAttachments: (fileCategoryArea: string, referenceId: number | string, attachmentInputs: IAttachFileInfo[], metaInfo?: {
        tenantId?: number;
        organisationGroupId?: number;
    }) => Promise<number[]>;
    resolveBatchEntityFileSize: (...args: any[]) => Promise<any>;
    resolveBatchEntityFileUrl: (...args: any[]) => Promise<any>;
}>;
export interface IFileAttachment {
    id: number;
    referenceId: number;
    fileId: number;
    description?: string;
    sequence: number;
    status: TFileAttachmentStatus;
    fileMetadataId: number;
    version: number;
    fileName: string;
    fileSize: number;
    mimeType: string;
    url: string;
    createdAt: DateTime;
}
export interface IFileMetadata {
    id: number;
    fileId: number;
    fileMetadataId: number;
    status: TFileAttachmentStatus;
    version: number;
    fileName: string;
    fileSize: number;
    mimeType: string;
    url: string;
}
export type TFileAttachmentStatus = 'A' | 'D';
export interface IAttachFileInfo {
    fileId?: number;
    uploadToken?: string;
    description?: string;
    fileName?: string;
}
interface INewUploadFile {
    fileId: number | null;
    tenantId?: number;
    organisationGroupId: number | null;
    fileName: string;
    mimeType: string;
    fileSize: number;
}
interface INewUploadFileResponse {
    ok: boolean;
    uploadToken: string;
    uploadUrl: string;
    uploadTarget: 'app' | 'aws';
    uploadConfig: IUploadConfig;
}
interface IUploadConfig {
    uploadUrl: string;
    signUrl: string;
    filePath: string;
    bucketName: string;
    region: string;
    accessKey: string;
    sessionToken: string;
}
export {};
