"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const constants_1 = require("../../constants");
const errorSerializer_1 = __importDefault(require("../../support/errorSerializer"));
const tagHandler = {
    get: function ({ serviceInfo, headers, tag }, operationId) {
        if (!serviceInfo.swaggerClient.apis[tag] ||
            !serviceInfo.swaggerClient.apis[tag][operationId]) {
            throw new Error(`service client [${serviceInfo.name}]: operation ${operationId} is not present in tag ${tag}`);
        }
        return (parameters) => __awaiter(this, void 0, void 0, function* () {
            try {
                const response = yield serviceInfo.swaggerClient.apis[tag][operationId](parameters, {
                    http: request => {
                        Object.assign(request.headers, headers);
                        return serviceInfo.swaggerClient.http(request);
                    },
                });
                if (response.status === 200) {
                    return response.body;
                }
                const op = `${tag}.${operationId}`;
                const rd = `HTTP ${response.status} ${response.statusText}`;
                throw new Error(`service request error: ${serviceInfo.name} ${op}: ${rd}`);
            }
            catch (error) {
                if (error.response) {
                    throw errorSerializer_1.default.deserialize(error.response.body);
                }
                throw error;
            }
        });
    },
};
const rootHandler = {
    get: function ({ serviceInfo, headers }, tag) {
        if (tag === 'then') {
            // i'm not a promise, no
            return undefined;
        }
        return new Proxy({ serviceInfo, headers, tag }, tagHandler);
    },
};
function makeServiceClient(serviceInfo, { requestId, accessToken, userId, userName }) {
    const headers = {
        [constants_1.REQUEST_ID_HEADER]: requestId,
        [constants_1.AUTHORIZATION_HEADER]: `Bearer ${accessToken}`,
        [constants_1.USER_ID_HEADER]: userId,
        [constants_1.USER_NAME_HEADER]: userName,
    };
    return new Proxy({ serviceInfo, headers }, rootHandler);
}
exports.default = makeServiceClient;
//# sourceMappingURL=makeServiceClient.js.map