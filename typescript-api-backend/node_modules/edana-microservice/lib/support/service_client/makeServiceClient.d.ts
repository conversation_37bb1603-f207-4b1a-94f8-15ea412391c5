import { AUTHORIZ<PERSON>ION_HEADER, REQUEST_ID_HEADER, USER_ID_HEADER, USER_NAME_HEADER } from '../../constants';
import { IServiceInfo } from '../service_registry/discoverMicroservice/loadServiceInfo';
export default function makeServiceClient<T extends ISwaggerClient>(serviceInfo: any, { requestId, accessToken, userId, userName }: IHeaders): T;
export interface IHeaders {
    requestId?: string;
    accessToken?: string;
    userId?: number;
    userName?: string;
}
export interface ISwaggerClient {
    serviceInfo: IServiceInfo;
    headers: {
        [REQUEST_ID_HEADER]?: string;
        [AUTHORIZATION_HEADER]?: string;
        [USER_ID_HEADER]?: number;
        [USER_NAME_HEADER]?: string;
    };
}
export interface ISwaggerClientDict extends ISwaggerClient {
    common: {
        [key: string]: (...args: any[]) => any;
    };
}
