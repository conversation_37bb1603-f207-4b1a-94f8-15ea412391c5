import { IContext } from '../interfaces';
import { DeleteResponse } from '../../index';
export interface IGetParam {
    [key: string]: number | string | number[] | string[] | null | undefined;
}
type TGet<T extends IGetParam> = {
    tenantId: number;
} & T;
interface IPost<T> {
    tenantId: number;
    body: {
        params: T;
    };
}
type TCommonFunction = (context: IContext, params: any) => any;
export type TGetResolverMapper<Func extends TCommonFunction> = (context: IContext, args: TGet<Omit<Parameters<Func>[1], 'tenantId'>>) => ReturnType<Func>;
export type TPostResolverMapper<Func extends TCommonFunction> = (context: IContext, args: IPost<Omit<Parameters<Func>[1], 'tenantId'>>) => ReturnType<Func>;
export type TPostUpdateResolverMapper<Func extends TCommonFunction> = (context: IContext, args: IPost<Omit<Parameters<Func>[1], 'tenantId' | 'id'>> & {
    id: number;
}) => ReturnType<Func>;
export type TDeleteResolverMapper<Func extends TCommonFunction> = (context: IContext, args: {
    id: number;
    tenantId: number;
}) => Promise<DeleteResponse>;
export type TGraphqlResolver<A, R, N = undefined> = (node: N, args: A, context: IContext) => Promise<R>;
export type TGraphqlBatchResolver<A, R, N = undefined> = (node: N[], args: A, context: IContext) => Promise<R[]>;
export {};
