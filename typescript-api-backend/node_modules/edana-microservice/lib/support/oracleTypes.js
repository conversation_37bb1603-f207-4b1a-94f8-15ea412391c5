"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ORACLE_TYPES = void 0;
const oracledb_1 = __importDefault(require("oracledb"));
exports.ORACLE_TYPES = [
    'DEFAULT',
    'DB_TYPE_VARCHAR',
    'DB_TYPE_NUMBER',
    'DB_TYPE_LONG',
    'DB_TYPE_DATE',
    'DB_TYPE_RAW',
    'DB_TYPE_LONG_RAW',
    'DB_TYPE_CHAR',
    'DB_TYPE_BINARY_FLOAT',
    'DB_TYPE_BINARY_DOUBLE',
    'DB_TYPE_ROWID',
    'DB_TYPE_CLOB',
    'DB_TYPE_BLOB',
    'DB_TYPE_TIMESTAMP',
    'DB_TYPE_TIMESTAMP_TZ',
    'DB_TYPE_TIMESTAMP_LTZ',
    'DB_TYPE_NVARCHAR',
    'DB_TYPE_NCHAR',
    'DB_TYPE_NCLOB',
    'STMT_TYPE_UNKNOWN',
    'STMT_TYPE_SELECT',
    'STMT_TYPE_UPDATE',
    'STMT_TYPE_DELETE',
    'STMT_TYPE_INSERT',
    'STMT_TYPE_CREATE',
    'STMT_TYPE_DROP',
    'STMT_TYPE_ALTER',
    'STMT_TYPE_BEGIN',
    'STMT_TYPE_DECLARE',
    'STMT_TYPE_CALL',
    'STMT_TYPE_EXPLAIN_PLAN',
    'STMT_TYPE_MERGE',
    'STMT_TYPE_ROLLBACK',
    'STMT_TYPE_COMMIT',
    'STRING',
    'NUMBER',
    'DATE',
    'CURSOR',
    'BUFFER',
    'CLOB',
    'BLOB',
    'SYSDBA',
    'SYSOPER',
    'SYSASM',
    'SYSBACKUP',
    'SYSDG',
    'SYSKM',
    'SYSRAC',
    'BIND_IN',
    'BIND_INOUT',
    'BIND_OUT',
    'ARRAY',
    'OBJECT',
].reduce((hash, type) => {
    hash[type] = oracledb_1.default[type];
    return hash;
}, {});
//# sourceMappingURL=oracleTypes.js.map