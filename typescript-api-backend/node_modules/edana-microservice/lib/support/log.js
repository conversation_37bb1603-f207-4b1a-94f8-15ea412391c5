"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeRequestLogger = exports.coreLogger = void 0;
const winston_1 = __importDefault(require("winston"));
const errorSerializer_1 = __importDefault(require("./errorSerializer"));
const config_1 = require("./config");
const transports = [
    new winston_1.default.transports.Console({
        colorize: !!(config_1.LOG_LEVEL === 'debug' || config_1.LOG_LEVEL === 'DEBUG'),
        timestamp: true,
        prettyPrint: !!(config_1.LOG_LEVEL === 'debug' || config_1.LOG_LEVEL === 'DEBUG'),
        json: !(config_1.LOG_LEVEL === 'debug' || config_1.LOG_LEVEL === 'DEBUG'),
        stringify: !(config_1.LOG_LEVEL === 'debug' || config_1.LOG_LEVEL === 'DEBUG'),
        level: config_1.LOG_LEVEL,
        handleExceptions: true,
        humanReadableUnhandledException: false,
    }),
];
exports.coreLogger = new winston_1.default.Logger({ transports });
exports.default = {
    makeRequestLogger,
};
function makeRequestLogger(koaContext) {
    return new winston_1.default.Logger({
        transports,
        rewriters: [addRequestMeta(koaContext)],
    });
}
exports.makeRequestLogger = makeRequestLogger;
function addRequestMeta(koaContext) {
    const { state: { id: requestId }, method: requestMethod, url: requestUrl, } = koaContext || { state: {} };
    return (level, msg, inputMeta) => {
        let meta = inputMeta;
        if (meta instanceof Error) {
            meta = {
                error: errorSerializer_1.default.serialize(meta),
            };
        }
        else if (meta.error && meta.error instanceof Error) {
            meta.error = errorSerializer_1.default.serialize(meta.error);
        }
        const metaRes = Object.assign({}, meta, { requestId });
        if (requestMethod) {
            Object.assign(meta, { requestMethod });
        }
        if (requestUrl) {
            Object.assign(meta, { requestUrl });
        }
        return metaRes;
    };
}
//# sourceMappingURL=log.js.map