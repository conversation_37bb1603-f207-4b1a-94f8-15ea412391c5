
/* This file is generated by glib-mkenums, do not modify it. This code is licensed under the same license as the containing project. Note that it links to GLib, so must comply with the LGPL linking clauses. */

#ifndef __GOBJECT_ENUM_TYPES_H__
#define __GOBJECT_ENUM_TYPES_H__

#include <glib-object.h>

G_BEGIN_DECLS

/* enumerations from "../gobject/../glib/gunicode.h" */
GLIB_AVAILABLE_IN_2_60 GType g_unicode_type_get_type (void) G_GNUC_CONST;
#define G_TYPE_UNICODE_TYPE (g_unicode_type_get_type ())
GLIB_AVAILABLE_IN_2_60 GType g_unicode_break_type_get_type (void) G_GNUC_CONST;
#define G_TYPE_UNICODE_BREAK_TYPE (g_unicode_break_type_get_type ())
GLIB_AVAILABLE_IN_2_60 GType g_unicode_script_get_type (void) G_GNUC_CONST;
#define G_TYPE_UNICODE_SCRIPT (g_unicode_script_get_type ())
GLIB_AVAILABLE_IN_2_60 GType g_normalize_mode_get_type (void) G_GNUC_CONST;
#define G_TYPE_NORMALIZE_MODE (g_normalize_mode_get_type ())
G_END_DECLS

#endif /* __GOBJECT_ENUM_TYPES_H__ */

/* Generated data ends here */

