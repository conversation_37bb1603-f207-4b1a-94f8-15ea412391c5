const jwt = require('jsonwebtoken');

const payload = {
  userId: 1,
  organisationGroupId: 1,
  userName: 'testuser',
  accessHash: 'testhash',
  tenantId: 1
};

const secret = 'secret';
const audience = 'edana2-dev.ed-space.net';

const token = jwt.sign(payload, secret, { 
  audience: audience,
  expiresIn: '1h'
});

console.log('Test JWT Token:');
console.log(token);
console.log('\nUse this in Authorization header as:');
console.log(`Authorization: Bearer ${token}`);
