import { Request, Response, NextFunction } from "express";
import {
  extractToken,
  getDomain,
  getTokenFromRequest,
  TokenPayload,
} from "../helpers/token";
// import { find, get } from "lodash"; // Not needed with simplified tenant lookup
import getAllTenants from "../services/tenant.service";
import { withConnection, IConnection, ICtx, IContext } from "edana-microservice";

/**
 * Create a temporary context for tenant resolution
 * This allows us to query the database before the main context is created
 */
async function createTemporaryContext(): Promise<IContext> {
  return new Promise((resolve, reject) => {
    // Create a minimal Koa context for withConnection
    const mockKoaCtx = {
      state: {
        id: `tenant_lookup_${Date.now()}`,
        log: {
          info: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP INFO:', message, ...args),
          error: (message: string, ...args: any[]) => console.error('TENANT_LOOKUP ERROR:', message, ...args),
          warn: (message: string, ...args: any[]) => console.warn('TENANT_LOOKUP WARN:', message, ...args),
          debug: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP DEBUG:', message, ...args),
          silly: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP SILLY:', message, ...args),
          verbose: (message: string, ...args: any[]) => console.log('TENANT_LOOKUP VERBOSE:', message, ...args),
        } as any,
        tenantId: 1, // Use a default tenant for the lookup query
        organisationGroupId: 1,
        userId: 1,
        userName: 'system_tenant_lookup',
        accessHash: 'system_lookup',
        token: 'system_lookup',
      },
      customProperties: {},
    } as unknown as ICtx;

    withConnection(mockKoaCtx, async (connection: IConnection) => {
      console.log('Temporary context created for tenant lookup');

      // Create a simple cache for tenant lookup
      const tenantCache = new Map<string, any>();

      // Create a minimal context with just what we need for tenant lookup
      const tempContext: IContext = {
        connection,
        cache: {
          wrap: async (key: string, loader: () => Promise<any>) => {
            if (tenantCache.has(key)) {
              console.log(`Tenant cache hit: ${key}`);
              return tenantCache.get(key);
            }
            console.log(`Tenant cache miss: ${key}`);
            const result = await loader();
            tenantCache.set(key, result);
            return result;
          },
          invalidate: async (key: string) => {
            tenantCache.delete(key);
          }
        } as any,
        tenantId: 1,
        userId: 1,
        userName: 'system_tenant_lookup',
        log: mockKoaCtx.state.log,
      } as any;

      resolve(tempContext);
    }).catch((error) => {
      console.error('Failed to create temporary context for tenant lookup:', error);
      reject(error);
    });
  });
}

// Extend Express Request to include auth state
declare global {
  namespace Express {
    interface Request {
      authState?: {
        token?: string;
        userId?: number;
        userName?: string;
        accessHash?: string;
        organisationGroupId?: number;
        tenantId?: number;
        isSubstitution?: boolean;
      };
    }
  }
}

/**
 * Express middleware equivalent to withCookieUserToken from edana2_api
 * Extracts JWT token from signed cookies
 */
export const withCookieUserToken = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  req.authState = req.authState || {};

  // Extract token from signed cookies (production-ready)
  const cookieSecret = process.env.COOKIE_SECRET;
  if (!cookieSecret) {
    res.status(500).json({
      success: false,
      error: "Server configuration error: COOKIE_SECRET not set",
    });
    return;
  }

  // Get domain-specific cookie key
  const domain = getDomain(req);
  const authKey = `Authorization_${domain}`;

  // Extract token from signed cookie
  if (req.signedCookies && req.signedCookies[authKey]) {
    req.authState.token = req.signedCookies[authKey];
  }

  next();
};

/**
 * Express middleware equivalent to withSubstitutionAuthentication from edana2_api
 * Checks for substitution authentication via signed cookies
 */
export const withSubstitutionAuthentication = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  req.authState = req.authState || {};

  if (typeof req.authState.isSubstitution === "undefined") {
    const domain = getDomain(req);
    const originAuthKey = `OriginalAuthorization_${domain}`;

    req.authState.isSubstitution = !!(
      req.signedCookies && req.signedCookies[originAuthKey]
    );
  }

  next();
};

/**
 * Express middleware equivalent to withUserAuthentication from edana2_api
 * Extracts and validates JWT token, populates user information
 */
export const withUserAuthentication = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  req.authState = req.authState || {};

  // Extract token from request (Authorization header or cookies)
  const token = req.authState.token || getTokenFromRequest(req);
  if (token) {
    req.authState.token = token;

    try {
      const payload = await extractToken(req, token);
      if (payload && payload.userId) {
        req.authState.userId = payload.userId;
        req.authState.userName = payload.userName;
        req.authState.accessHash = payload.accessHash;
        req.authState.organisationGroupId = payload.organisationGroupId;
        if (payload.tenantId) {
          req.authState.tenantId = payload.tenantId;
        }
      }
    } catch (e) {
      console.warn("Invalid JWT token:", e);
      res.status(403).json({
        success: false,
        error: "Invalid authentication token",
      });
      return;
    }
  }

  next();
};


/**
 * Express middleware equivalent to withTenant from edana2_api
 * Resolves tenant information from domain using database/cache lookup
 */
export const withTenant = async (
  req: Request,
  _res: Response,
  next: NextFunction
): Promise<void> => {
  req.authState = req.authState || {};

  try {
    // Get domain from request
    const domain = getDomain(req);
    console.log('Resolving tenant for domain:', domain);

    // Create temporary context for tenant lookup
    const tempContext = await createTemporaryContext();

    // Fetch all tenants from database/cache
    const tenants = await getAllTenants(tempContext);
    console.log(`Loaded ${tenants.length} tenants for domain matching`);

    // Find tenant by domain
    const tenant = tenants.find((t) => t.domain === domain);

    if (tenant && tenant.status === 'ACTIVE') { // Active tenant
      req.authState.tenantId = tenant.id;
      console.log(`Found active tenant: ${tenant.siteName} (ID: ${tenant.id}) for domain: ${domain}`);
    } else {
      // Fallback to JWT token tenant or default
      const fallbackTenantId = req.authState.tenantId || 2;
      req.authState.tenantId = fallbackTenantId;
      console.log(`No active tenant found for domain: ${domain}, using fallback tenant ID: ${fallbackTenantId}`);
    }

  } catch (error) {
    console.error('Error in tenant resolution:', error);

    // Error fallback - use JWT token tenant or default
    const fallbackTenantId = req.authState.tenantId || 2;
    req.authState.tenantId = fallbackTenantId;
    console.log(`Error fallback: using tenant ID: ${fallbackTenantId}`);
  }

  next();
};

/**
 * Middleware to ensure user is authenticated
 */
export const requireAuth = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.authState?.userId) {
    res.status(401).json({ error: "Authentication required" });
    return;
  }
  next();
};

/**
 * Middleware to ensure tenant is resolved
 */
export const requireTenant = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  if (!req.authState?.tenantId) {
    res.status(403).json({ error: "Invalid tenant" });
    return;
  }
  next();
};
