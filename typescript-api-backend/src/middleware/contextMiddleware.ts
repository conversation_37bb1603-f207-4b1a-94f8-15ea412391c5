import { Request, Response, NextFunction } from 'express';
import { IContext, IConnection, withConnection, ICtx } from 'edana-microservice';
import { extractToken } from '../helpers/token';

/**
 * Create a real Oracle database connection using the edana-microservice framework
 * This uses the withConnection function which handles connection pooling automatically
 */
async function createRealOracleConnection(
  tenantId: number,
  userId: number,
  organisationGroupId?: number
): Promise<IConnection> {
  // Create a mock Koa context for the withConnection function
  const mockKoaCtx = {
    state: {
      id: `req_${Date.now()}`,
      log: {
        info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
        error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
        warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
        debug: (message: string, ...args: any[]) => console.log('DEBUG:', message, ...args),
        silly: (message: string, ...args: any[]) => console.log('SILLY:', message, ...args),
        verbose: (message: string, ...args: any[]) => console.log('VERBOSE:', message, ...args),
      } as any,
      tenantId,
      organisationGroupId,
      userId,
      userName: `user_${userId}`,
      accessHash: 'mock_access_hash',
      token: 'mock_token',
    },
    customProperties: {},
  } as unknown as ICtx;

  return new Promise((resolve, reject) => {
    withConnection(mockKoaCtx, async (connection: IConnection) => {
      console.log('Real Oracle connection established via edana-microservice');
      resolve(connection);
    }).catch((error) => {
      console.error('Failed to create real Oracle connection:', error);
      // Fallback to a mock connection if Oracle connection fails
      const mockConnection: IConnection = {
        async commit() {
          console.log('FALLBACK: Transaction committed');
        },
        async rollback() {
          console.log('FALLBACK: Transaction rolled back');
        },
        execute: async (sql: string, binds?: any, options?: any) => {
          console.log('FALLBACK: Mock database execute:', sql);
          return { rows: [], outBinds: {} };
        },
        executeMany: async (sql: string, binds?: any[], options?: any) => {
          console.log('FALLBACK: Mock database executeMany:', sql);
          return { rowsAffected: 0 };
        },
        executeAll: async (query: string) => {
          console.log('FALLBACK: Mock database executeAll:', query);
        },
        close: async () => {
          console.log('FALLBACK: Mock database close');
        },
      } as any;
      resolve(mockConnection);
    });
  });
}

// Mock cache implementation
const mockCache = {
  wrap: async (key: string, fn: () => Promise<any>) => {
    console.log(`Cache lookup for key: ${key}`);
    return await fn();
  },
  get: async (key: string) => {
    console.log(`Cache get for key: ${key}`);
    return null;
  },
  set: async (key: string, value: any, ttl?: number) => {
    console.log(`Cache set for key: ${key}, TTL: ${ttl}`);
  },
  del: async (key: string) => {
    console.log(`Cache delete for key: ${key}`);
  },
  clear: async () => {
    console.log('Cache clear');
  },
} as any;

// Mock logger
const mockLogger = {
  info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
  error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
  warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
  debug: (message: string, ...args: any[]) => console.log('DEBUG:', message, ...args),
  silly: (message: string, ...args: any[]) => console.log('SILLY:', message, ...args),
  verbose: (message: string, ...args: any[]) => console.log('VERBOSE:', message, ...args),
} as any;

// Service client registry
const serviceClients = new Map<string, any>();

// Register mock service clients
serviceClients.set('edana_api_fs', {
  files: {
    deleteFiles: async (params: { fileIds: number[] }) => {
      console.log('Service client: deleting files', params.fileIds);
    },
  },
});

serviceClients.set('edana2_api_notf', {
  notifications: {
    send: async (params: any) => {
      console.log('Service client: sending notification', params);
    },
  },
});

// Enhanced context interface for Express requests
declare global {
  namespace Express {
    interface Request {
      context?: IContext;
    }
  }
}

/**
 * Middleware to create and attach microservice context to requests
 */
export const contextMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Extract tenant and user information from auth middleware or headers
    const tenantId = req.authState?.tenantId || 2; // Default fallback
    const organisationGroupId = req.authState?.organisationGroupId || 1; // Default fallback
    const userId = req.authState?.userId || 1; // Default fallback
    const userName = req.authState?.userName || `user_${userId}`;

    // Create a real Oracle database connection using edana-microservice
    const oracleConnection = await createRealOracleConnection(tenantId, userId, organisationGroupId);

    // Create base context matching the real IContext interface
    const baseContext: Omit<IContext, 'fsClient'> = {
      connection: oracleConnection,
      cache: mockCache,
      tenantId,
      isSuperTenant: false,
      organisationGroupId,
      organisationsIds: [organisationGroupId],
      userId,
      userName,
      log: mockLogger,
      redisConnectionFactory: null,
      requireServiceClient: async (serviceName: string) => {
        const client = serviceClients.get(serviceName);
        if (!client) {
          throw new Error(`Service client not found: ${serviceName}`);
        }
        return client;
      },
      raiseHumanError: (errors: any) => { throw new Error('Human error'); },
      raiseInvalidRequestError: (description: string) => { throw new Error(description); },
      raiseNotAuthenticatedError: () => { throw new Error('Not authenticated'); },
      raiseNotAuthorizedError: () => { throw new Error('Not authorized'); },
      raiseNotFoundError: (entity: { name: string }) => { throw new Error(`${entity.name} not found`); },
      raiseInternalServerError: () => { throw new Error('Internal server error'); },
      raiseNotImplementedError: () => { throw new Error('Not implemented'); },
      raiseServiceUnavailableError: () => { throw new Error('Service unavailable'); },
      raiseNotUniqueError: () => { throw new Error('Not unique'); },
      ctx: {} as any,
    };

    // Create a mock fsClient since the real withFsClient is designed for Koa
    // In a real implementation, you would integrate with the actual file service
    const fsClient = Promise.resolve({
      addEntityAttachments: async (fileCategoryArea: string, referenceId: number | string, attachmentInputs: any[]) => {
        console.log('Mock fsClient: addEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs });
        return attachmentInputs.map((_, index) => ({ id: index + 1 }));
      },
      getFileCategoryInfo: async () => {
        console.log('Mock fsClient: getFileCategoryInfo');
        return { fileCategories: [], categoryArea: [], categoryGroups: [] };
      },
      getStorageInstances: async (params: any) => {
        console.log('Mock fsClient: getStorageInstances', params);
        return [];
      },
      initiateAttachmentUpload: async (fileCategoryAreaKey: string, files: any[]) => {
        console.log('Mock fsClient: initiateAttachmentUpload', { fileCategoryAreaKey, files });
        return files.map(() => ({ ok: true, uploadToken: 'mock-token', uploadUrl: 'mock-url', uploadTarget: 'app' as const, uploadConfig: {} as any }));
      },
      loadEntityAttachmentByFileId: async (fileId: number) => {
        console.log('Mock fsClient: loadEntityAttachmentByFileId', fileId);
        return {
          id: fileId,
          fileId,
          fileMetadataId: fileId,
          status: 'A' as const,
          version: 1,
          fileName: `file_${fileId}.txt`,
          fileSize: 1024,
          mimeType: 'text/plain',
          url: `http://localhost:3001/files/${fileId}/download`,
        };
      },
      loadEntityAttachments: async (fileCategoryArea: string, referenceId: string | number, options?: any) => {
        console.log('Mock fsClient: loadEntityAttachments', { fileCategoryArea, referenceId, options });
        return [];
      },
      bulkLoadEntityAttachments: async (fileCategoryArea: string, referenceIds: string[] | number[], options?: any) => {
        console.log('Mock fsClient: bulkLoadEntityAttachments', { fileCategoryArea, referenceIds, options });
        return [];
      },
      bulkLoadEntityAttachmentsCount: async (fileCategoryArea: string, referenceIds: string[] | number[], options?: any) => {
        console.log('Mock fsClient: bulkLoadEntityAttachmentsCount', { fileCategoryArea, referenceIds, options });
        return 0;
      },
      updateEntityAttachments: async (fileCategoryArea: string, referenceId: number | string, attachmentInputs: any[], metaInfo?: any) => {
        console.log('Mock fsClient: updateEntityAttachments', { fileCategoryArea, referenceId, attachmentInputs, metaInfo });
        return attachmentInputs.map((_, index) => index + 1);
      },
      resolveBatchEntityFileSize: async (...args: any[]) => {
        console.log('Mock fsClient: resolveBatchEntityFileSize', args);
        return {};
      },
      resolveBatchEntityFileUrl: async (...args: any[]) => {
        console.log('Mock fsClient: resolveBatchEntityFileUrl', args);
        return {};
      },
    });

    // Create the complete context
    const contextWithFs: IContext = {
      ...baseContext,
      fsClient,
    };

    // Attach context to request
    req.context = contextWithFs;

    next();
  } catch (error) {
    console.error('Context middleware error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initialize request context',
    });
  }
};

/**
 * Helper function to get context from request
 */
export const getContext = (req: Request): IContext => {
  if (!req.context) {
    throw new Error('Context not available. Ensure contextMiddleware is applied.');
  }
  return req.context;
};

/**
 * Decorator to inject context into controller methods
 */
export const WithContext = (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
  const method = descriptor.value;

  descriptor.value = function (...args: any[]) {
    // Find the request object in the arguments
    const req = args.find(arg => arg && arg.context);
    if (req && req.context) {
      // Replace the mock context creation with the real context
      return method.apply(this, args);
    }
    return method.apply(this, args);
  };

  return descriptor;
};
