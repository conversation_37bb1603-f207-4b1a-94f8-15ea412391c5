import { utils } from 'edana-microservice';

const CREATION_IN_PROGRESS: TTenantStatus = 'CREATION_IN_PROGRESS';
const ACTIVE: TTenantStatus = 'ACTIVE';
const INACTIVE: TTenantStatus = 'INACTIVE';
export const DELETED: TTenantStatus = 'DELETED';
const REMOVING: TTenantStatus = 'REMOVING';
const DEPLOY_FAILED: TTenantStatus = 'DEPLOY_FAILED';

export type TTenantStatus =
  | 'CREATION_IN_PROGRESS'
  | 'ACTIVE'
  | 'INACTIVE'
  | 'DELETED'
  | 'REMOVING'
  | 'DEPLOY_FAILED';

const Ids = {
  [CREATION_IN_PROGRESS]: 0,
  [ACTIVE]: 1,
  [REMOVING]: 2,

  [INACTIVE]: -1,
  [DELETED]: -2,
  [DEPLOY_FAILED]: -3,
};

const { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(
  Ids,
);

export default {
  CREATION_IN_PROGRESS,
  ACTIVE,
  INACTIVE,
  DELETED,
  REMOVING,
  DEPLOY_FAILED,

  valueById,
  idByValue,
  serialize,
  deserialize,
};
