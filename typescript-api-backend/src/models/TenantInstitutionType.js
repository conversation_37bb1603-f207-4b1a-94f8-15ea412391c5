const EDUCATION = 'EDUCATION';
const NON_EDUCATION = 'NON_EDUCATION';
const MIXED = 'MIXED';

const Ids = {
  [EDUCATION]: 1,
  [NON_EDUCATION]: 2,
  [MIXED]: 3,
};

const { utils } = require('edana-microservice');
const { valueById, idByValue, serialize, deserialize } = utils.getModelCommon(
  Ids,
);

module.exports = {
  EDUCATION,
  NON_EDUCATION,
  MIXED,
  serialize,
  deserialize,
  valueById,
  idByValue,
};
