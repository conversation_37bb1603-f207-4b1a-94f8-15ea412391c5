import jwt from "jsonwebtoken";
import { Request } from "express";

export interface TokenPayload {
  userId: number;
  organisationGroupId: number;
  userName: string;
  accessHash: string;
  tenantId?: number;
}

/**
 * Extract JWT token from Express request with proper audience validation
 * Production-ready version with security checks
 */
export function extractToken(
  req: Request,
  token: string
): Promise<TokenPayload | null> {
  return new Promise((resolve, reject) => {
    const secret = process.env.JWT_SECRET;
    if (!secret) {
      reject(new Error("JWT_SECRET environment variable is required"));
      return;
    }

    // Get audience from request domain
    const domain = getDomain(req);

    // For Bearer tokens, use the token directly
    // For cookie tokens, extract from cookie string
    let jwtToken = token;
    if (token && token.includes('=')) {
      // This looks like a cookie string, extract the JWT
      const authKey = `Authorization_${domain}`;
      jwtToken = extractJwt(token, authKey);
    }

    if (!jwtToken) {
      resolve(null);
      return;
    }

    jwt.verify(jwtToken, secret, { audience: domain }, (err, payload) => {
      if (err) {
        if (
          err instanceof jwt.TokenExpiredError ||
          err.message.includes("audience invalid")
        ) {
          resolve(null);
        } else {
          reject(err);
        }
      } else {
        resolve(payload as TokenPayload);
      }
    });
  });
}

function isDev(): boolean {
  return process.env.ENV === "development";
}

export function getDomain(req: Request): string {
  return isDev()
    ? process.env.AUDIENCE_TEMPLATE || req.get("host") || "localhost"
    : req.get("host") || "localhost";
}
function extractJwt(
  cookieString: string,
  key: string,
): string | null {
  if (!cookieString) return null;

  // Split cookies by ;
  const cookies = cookieString.split(";").map((c) => c.trim());

  // Find the one that starts with the given key
  const target = cookies.find((c) => c.startsWith(key + "="));
  if (!target) return null;

  // Return the value after =
  return target.split("=")[1] || null;
}

/**
 * Extract token from Authorization header or signed cookies
 * Production-ready version with proper cookie handling
 */
export function getTokenFromRequest(req: Request): string | undefined {
  // Try Authorization header first (Bearer token)
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // Try signed cookies (production approach)
  if (req.signedCookies) {
    const domain = getDomain(req);
    const authKey = `Authorization_${domain}`;

    if (req.signedCookies[authKey]) {
      return req.signedCookies[authKey];
    }
  }

  // Fallback to regular cookies for development
  const cookieToken = req.headers.cookie
    ?.split(";")
    .find((c) => c.trim().startsWith("auth_token="))
    ?.split("=")[1];

  return cookieToken;
}
