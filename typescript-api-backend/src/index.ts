import 'reflect-metadata';
import { config } from 'dotenv';
import express from 'express';

// Load environment variables
config();
import { useContainer, useExpressServer } from 'routing-controllers';
import { Container } from 'typedi';
import { HelloController } from '@webaverse/controllers/hello.controller';
import { EContentContentController } from '@webaverse/controllers/eContentContent.controller';
import { FilesController } from '@webaverse/controllers/files.controller';
import { contextMiddleware } from '@webaverse/middleware/contextMiddleware';
import {
  withUserAuthentication,
  withTenant,
  requireAuth,
  requireTenant
} from './middleware/authMiddleware';

export const Search = {
    start: () => {
        useContainer(Container);

        const app = express();

        app.use(function (req, res, next) {
            req.setTimeout(0); // no timeout for all requests, your server will be DoS'd
            next();
        });

        app.use(express.urlencoded({ extended: true }));
        app.use(express.json());

        // Security Configuration
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            throw new Error('JWT_SECRET environment variable is required for production security');
        }

        // Authentication middleware chain (production-ready security)
        app.use('/api', withUserAuthentication);
        app.use('/api', withTenant);

        // Security enforcement middleware - REQUIRES VALID AUTHENTICATION
        app.use('/api', requireAuth);
        app.use('/api', requireTenant);

        // Apply microservice context middleware after tenant resolution
        app.use('/api', contextMiddleware);

        // Configure controllers
        useExpressServer(app, {
            routePrefix: '/api',
            controllers: [
                HelloController,
                EContentContentController,
                FilesController
            ],
        });

        // Test endpoints without authentication
        app.post('/test/upload', (req, res) => {
            res.json({
                success: true,
                message: 'Test upload endpoint working',
                data: {
                    uploadToken: `test-token-${Date.now()}`,
                    fileId: Math.floor(Math.random() * 1000) + 1
                }
            });
        });

        app.get('*', (req, res, next) => {
            if (req.path.startsWith('/api')) {
                next();
            } else {
                res.end();
            }
        });

        const server = app.listen(process.env.PORT || 3000, () => {
            // eslint-disable-next-line no-console
            console.log(`⚡️[server]: Server is running at https://localhost:${process.env.PORT || 3000}`);
        });

        server.setTimeout(0);
    },
};

Search.start();