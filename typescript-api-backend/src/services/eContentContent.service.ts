import { Service } from 'typedi';
import { IContext, DateTime } from 'edana-microservice';
import EContentContent from '../entities/EContentContent';
import EContentClob from '../entities/EContentClob';
import { IEContentContent, IEContentContentCreate, IEContentContentUpdate } from '../interfaces/IEContentContent';
import { IEContentClob, IEContentClobCreate } from '../interfaces/IEContentClob';
import { IAttachFileInfo } from '../interfaces/IAttachFileInfo';
import { E_CONTENT_CONTENT_FILE } from '../constants/fsCategoriesAreas';

@Service()
export class EContentContentService {
  
  async createEContentContent(
    context: IContext,
    params: IEContentContentCreate
  ): Promise<IEContentContent> {
    const { connection, tenantId, organisationGroupId, userId } = context;
    const fsClient = await context.fsClient;
    
    // Extract clobs and other data
    const { clobs, questionOptions, ...contentData } = params;
    
    // Create the main content entity
    const content = await EContentContent.create(connection, {
      ...contentData,
      tenantId,
      createdBy: userId,
      createdAt: DateTime.utc(),
      updatedBy: userId,
      updatedAt: DateTime.utc(),
    });

    // Process CLOBs with file attachments
    for (const clobData of clobs) {
      const { attachments = [], ...clobFields } = clobData;
      
      // Create the CLOB entity
      const clob = await EContentClob.create(connection, {
        ...clobFields,
        contentId: content.id,
        tenantId,
      });

      // Handle file attachments if any
      if (attachments.length > 0) {
        await fsClient.updateEntityAttachments(
          E_CONTENT_CONTENT_FILE,
          clob.id,
          attachments,
          { tenantId, organisationGroupId }
        );
      }
    }

    // Commit transaction
    await connection.commit();
    
    return content;
  }

  async updateEContentContent(
    context: IContext,
    params: IEContentContentUpdate
  ): Promise<IEContentContent> {
    const { connection, tenantId, userId } = context;
    
    const { clobs, questionOptions, ...contentData } = params;
    
    // Update the main content entity
    const content = await EContentContent.update(connection, {
      ...contentData,
      tenantId,
      updatedBy: userId,
      updatedAt: DateTime.utc(),
    });

    // Handle CLOB updates (simplified for this implementation)
    for (const clobData of clobs) {
      if (clobData.id) {
        await EContentClob.update(connection, {
          ...clobData,
          tenantId,
        });
      } else {
        await EContentClob.create(connection, {
          ...clobData,
          contentId: content.id,
          tenantId,
        });
      }
    }

    await connection.commit();
    
    return content;
  }

  async getEContentContent(
    context: IContext,
    id: number
  ): Promise<IEContentContent> {
    const { connection, tenantId } = context;
    
    return await EContentContent.findBy(connection, { id, tenantId });
  }

  async getEContentContents(
    context: IContext,
    params: {
      itemId?: number;
      resourceLanguageId?: number;
      first?: number;
      count?: number;
      searchQuery?: string;
    }
  ): Promise<IEContentContent[]> {
    const { connection, tenantId } = context;
    
    const criteria: any = { tenantId };
    if (params.itemId) criteria.itemId = params.itemId;
    if (params.resourceLanguageId) criteria.resourceLanguageId = params.resourceLanguageId;
    return await EContentContent.loadBy(connection, criteria, {
      first: params.first || 0,
      count: params.count || 10,
    });
  }

  async deleteEContentContent(
    context: IContext,
    id: number
  ): Promise<void> {
    const { connection, tenantId, userId } = context;
    
    // Soft delete by updating status
    await EContentContent.update(connection, {
      id,
      tenantId,
      status: 'DELETED',
      updatedBy: userId,
      updatedAt: DateTime.utc(),
    });

    await connection.commit();
  }
}
