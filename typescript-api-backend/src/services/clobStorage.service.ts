import { Service } from 'typedi';
import { IContext } from 'edana-microservice';
import { IAttachFileInfo } from '../interfaces/IAttachFileInfo';

@Service()
export class ClobStorageService {
  
  /**
   * Store CLOB data with file attachments
   */
  async storeClobWithAttachments(
    context: IContext,
    clobContent: string,
    attachments: IAttachFileInfo[],
    categoryKey: string,
    entityId: number
  ): Promise<{ clobId: number; fileIds: number[] }> {
    const fsClient = await context.fsClient;
    const { tenantId, organisationGroupId } = context;
    
    // Store file attachments
    const fileIds = await fsClient.updateEntityAttachments(
      categoryKey,
      entityId,
      attachments,
      { tenantId, organisationGroupId }
    );
    
    // In a real implementation, you would also store the CLOB content
    // in a database with references to the file IDs
    const clobId = Math.floor(Math.random() * 10000); // Mock CLOB ID
    
    console.log(`Stored CLOB ${clobId} with ${fileIds.length} attachments`);
    
    return { clobId, fileIds };
  }
  
  /**
   * Retrieve CLOB data with associated files
   */
  async retrieveClobWithAttachments(
    context: IContext,
    clobId: number,
    categoryKey: string,
    entityId: number
  ): Promise<{ content: string; attachments: IAttachFileInfo[] }> {
    const fsClient = await context.fsClient;
    
    // Load associated file attachments
    const attachments = await fsClient.loadEntityAttachments(categoryKey, entityId);
    
    // In a real implementation, you would retrieve the actual CLOB content from database
    const content = `Mock CLOB content for ID ${clobId}`;
    
    return { content, attachments };
  }
  
  /**
   * Update CLOB content and manage file attachments
   */
  async updateClobWithAttachments(
    context: IContext,
    clobId: number,
    newContent: string,
    newAttachments: IAttachFileInfo[],
    categoryKey: string,
    entityId: number,
    removeExistingFiles: boolean = false
  ): Promise<{ fileIds: number[] }> {
    const fsClient = await context.fsClient;
    const { tenantId, organisationGroupId } = context;
    
    // Remove existing files if requested
    if (removeExistingFiles) {
      const existingAttachments = await fsClient.loadEntityAttachments(categoryKey, entityId);
      const existingFileIds = existingAttachments.map(att => att.fileId).filter(Boolean) as number[];
      
      if (existingFileIds.length > 0) {
        // Note: The real fsClient doesn't have a files.deleteFiles method
        // In a real implementation, you would need to use the appropriate service client
        console.log('Would delete existing files:', existingFileIds);
      }
    }
    
    // Store new attachments
    const fileIds = await fsClient.updateEntityAttachments(
      categoryKey,
      entityId,
      newAttachments,
      { tenantId, organisationGroupId }
    );
    
    console.log(`Updated CLOB ${clobId} with ${fileIds.length} new attachments`);
    
    return { fileIds };
  }
  
  /**
   * Delete CLOB and associated files
   */
  async deleteClobWithAttachments(
    context: IContext,
    clobId: number,
    categoryKey: string,
    entityId: number
  ): Promise<void> {
    const fsClient = await context.fsClient;
    
    // Load and delete associated files
    const attachments = await fsClient.loadEntityAttachments(categoryKey, entityId);
    const fileIds = attachments.map(att => att.fileId).filter(Boolean) as number[];
    
    if (fileIds.length > 0) {
      // Note: The real fsClient doesn't have a files.deleteFiles method
      // In a real implementation, you would need to use the appropriate service client
      console.log('Would delete files:', fileIds);
    }
    
    // In a real implementation, you would also delete the CLOB record from database
    console.log(`Deleted CLOB ${clobId} and ${fileIds.length} associated files`);
  }
  
  /**
   * Validate file attachments for CLOB storage
   */
  validateAttachments(attachments: IAttachFileInfo[]): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const maxFileSize = 10 * 1024 * 1024; // 10MB
    const allowedMimeTypes = [
      'text/plain',
      'text/html',
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    
    for (const attachment of attachments) {
      if (attachment.fileSize > maxFileSize) {
        errors.push(`File ${attachment.fileName} exceeds maximum size of 10MB`);
      }
      
      if (!allowedMimeTypes.includes(attachment.mimeType)) {
        errors.push(`File ${attachment.fileName} has unsupported type: ${attachment.mimeType}`);
      }
      
      if (!attachment.fileName || attachment.fileName.trim() === '') {
        errors.push('File name cannot be empty');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
