import { Service } from 'typedi';
import { IContext } from 'edana-microservice';

@Service()
export class MicroserviceContextService {
  
  /**
   * Validate context for microservice operations
   */
  validateContext(context: IContext): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!context.tenantId || context.tenantId <= 0) {
      errors.push('Invalid tenant ID');
    }
    
    if (!context.organisationGroupId || context.organisationGroupId <= 0) {
      errors.push('Invalid organisation group ID');
    }
    
    if (!context.userId || context.userId <= 0) {
      errors.push('Invalid user ID');
    }
    
    if (!context.connection) {
      errors.push('Database connection not available');
    }
    
    if (!context.fsClient) {
      errors.push('File system client not available');
    }
    
    return {
      valid: errors.length === 0,
      errors,
    };
  }
  
  /**
   * Create a child context with modified properties
   */
  createChildContext(parentContext: IContext, overrides: Partial<IContext>): IContext {
    return {
      ...parentContext,
      ...overrides,
    };
  }
  
  /**
   * Execute operation with transaction support
   */
  async withTransaction<T>(
    context: IContext,
    operation: (context: IContext) => Promise<T>
  ): Promise<T> {
    const { connection } = context;
    
    try {
      // Note: Real Oracle connections handle transactions differently
      // In a real implementation, you would use proper transaction management
      const result = await operation(context);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      throw error;
    }
  }
  
  /**
   * Execute operation with caching support
   */
  async withCache<T>(
    context: IContext,
    cacheKey: string,
    operation: () => Promise<T>,
    ttl?: number
  ): Promise<T> {
    const { cache } = context;
    
    return await cache.wrap(cacheKey, operation);
  }
  
  /**
   * Log context information for debugging
   */
  logContext(context: IContext, operation: string): void {
    console.log(`[${operation}] Context:`, {
      tenantId: context.tenantId,
      organisationGroupId: context.organisationGroupId,
      userId: context.userId,
      timestamp: new Date().toISOString(),
    });
  }
  
  /**
   * Create a sanitized context for logging (removes sensitive data)
   */
  sanitizeContextForLogging(context: IContext): any {
    return {
      tenantId: context.tenantId,
      organisationGroupId: context.organisationGroupId,
      userId: context.userId,
      hasConnection: !!context.connection,
      hasFsClient: !!context.fsClient,
      hasCache: !!context.cache,
    };
  }
}
