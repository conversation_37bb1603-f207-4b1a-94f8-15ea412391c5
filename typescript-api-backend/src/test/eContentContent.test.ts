import { EContentContentService } from '../services/eContentContent.service';
import { ClobStorageService } from '../services/clobStorage.service';
import { MicroserviceContextService } from '../services/microserviceContext.service';
import { IContext } from 'edana-microservice';
import { IEContentContentCreate } from '../interfaces/IEContentContent';
import { IAttachFileInfo } from '../interfaces/IAttachFileInfo';

// Mock context for testing
async function createTestContext(): Promise<IContext> {
  const mockLogger = {
    info: (message: string, ...args: any[]) => console.log('INFO:', message, ...args),
    error: (message: string, ...args: any[]) => console.error('ERROR:', message, ...args),
    warn: (message: string, ...args: any[]) => console.warn('WARN:', message, ...args),
    debug: (message: string, ...args: any[]) => console.log('DEBUG:', message, ...args),
  } as any;

  const mockConnection = {
    commit: async () => console.log('Test: Transaction committed'),
    rollback: async () => console.log('Test: Transaction rolled back'),
    execute: async () => ({ rows: [], outBinds: {} }),
    executeMany: async () => ({ rowsAffected: 0 }),
  } as any;

  const mockCache = {
    wrap: async (key: string, fn: () => Promise<any>) => await fn(),
    get: async () => null,
    set: async () => {},
    del: async () => {},
    clear: async () => {},
  } as any;

  const fsClient = Promise.resolve({
    addEntityAttachments: async () => [{ id: 1 }],
    getFileCategoryInfo: async () => ({ fileCategories: [], categoryArea: [], categoryGroups: [] }),
    getStorageInstances: async () => [],
    initiateAttachmentUpload: async () => [{ ok: true, uploadToken: 'mock', uploadUrl: 'mock', uploadTarget: 'app' as const, uploadConfig: {} as any }],
    loadEntityAttachmentByFileId: async () => ({ id: 1, fileId: 1, fileMetadataId: 1, status: 'A' as const, version: 1, fileName: 'test.txt', fileSize: 1024, mimeType: 'text/plain', url: 'mock-url' }),
    loadEntityAttachments: async () => [],
    bulkLoadEntityAttachments: async () => [],
    bulkLoadEntityAttachmentsCount: async () => 0,
    updateEntityAttachments: async () => [1, 2, 3],
    resolveBatchEntityFileSize: async () => ({}),
    resolveBatchEntityFileUrl: async () => ({}),
  });

  return {
    connection: mockConnection,
    cache: mockCache,
    tenantId: 1,
    isSuperTenant: false,
    organisationGroupId: 1,
    organisationsIds: [1],
    userId: 1,
    userName: 'test_user',
    log: mockLogger,
    redisConnectionFactory: null,
    fsClient,
    requireServiceClient: async (serviceName: string) => ({
      files: { deleteFiles: async () => {} },
    }),
    raiseHumanError: () => { throw new Error('Human error'); },
    raiseInvalidRequestError: (description: string) => { throw new Error(description); },
    raiseNotAuthenticatedError: () => { throw new Error('Not authenticated'); },
    raiseNotAuthorizedError: () => { throw new Error('Not authorized'); },
    raiseNotFoundError: (entity: { name: string }) => { throw new Error(`${entity.name} not found`); },
    raiseInternalServerError: () => { throw new Error('Internal server error'); },
    raiseNotImplementedError: () => { throw new Error('Not implemented'); },
    raiseServiceUnavailableError: () => { throw new Error('Service unavailable'); },
    raiseNotUniqueError: () => { throw new Error('Not unique'); },
    ctx: {} as any,
  };
}

// Test data
const testEContentData: IEContentContentCreate = {
  tenantId: 1,
  itemId: 100,
  resourceLanguageId: 1,
  description: 'Test EContent Content',
  sequence: 1,
  status: 'ACTIVE',
  languageId: 1,
  resourceId: 1,
  organisationGroupId: 1,
  clobs: [
    {
      id: 1,
      tenantId: 1,
      resourceId: 1,
      itemId: 100,
      contentId: 1,
      languageId: 1,
      resourceAttributeId: 1,
      attributeId: 1,
      content: 'This is test CLOB content with file attachments',
      groupSequence: 1,
      contentClobTypeId: 1,
      resourceCategoryId: 1,
      attachments: [
        {
          fileName: 'test-document.txt',
          fileSize: 1024,
          mimeType: 'text/plain',
          content: 'This is test file content',
        },
        {
          fileName: 'test-image.png',
          fileSize: 2048,
          mimeType: 'image/png',
          content: Buffer.from('fake-image-data'),
        },
      ],
    },
  ],
  questionOptions: [],
};

const testAttachments: IAttachFileInfo[] = [
  {
    fileName: 'standalone-file.pdf',
    fileSize: 5120,
    mimeType: 'application/pdf',
    content: Buffer.from('fake-pdf-content'),
  },
];

// Test functions
async function testEContentContentService() {
  console.log('\n=== Testing EContentContentService ===');
  
  const service = new EContentContentService();
  const context = await createTestContext();

  try {
    // Test create
    console.log('1. Testing createEContentContent...');
    const created = await service.createEContentContent(context, testEContentData);
    console.log('✓ Created EContentContent:', { id: created.id, description: created.description });

    // Test get
    console.log('2. Testing getEContentContent...');
    const retrieved = await service.getEContentContent(context, created.id);
    console.log('✓ Retrieved EContentContent:', { id: retrieved.id });

    // Test list
    console.log('3. Testing getEContentContents...');
    const list = await service.getEContentContents(context, { itemId: 100 });
    console.log('✓ Listed EContentContents:', list.length, 'items');

    // Test update
    console.log('4. Testing updateEContentContent...');
    const updated = await service.updateEContentContent(context, {
      ...testEContentData,
      id: created.id,
      description: 'Updated description',
      headingSearchTerm: 'updated',
    });
    console.log('✓ Updated EContentContent:', { id: updated.id });

    console.log('✓ EContentContentService tests passed!');
  } catch (error) {
    console.error('✗ EContentContentService test failed:', error.message);
  }
}

async function testClobStorageService() {
  console.log('\n=== Testing ClobStorageService ===');
  
  const service = new ClobStorageService();
  const context = await createTestContext();

  try {
    // Test validation
    console.log('1. Testing validateAttachments...');
    const validation = service.validateAttachments(testAttachments);
    console.log('✓ Validation result:', validation);

    // Test store CLOB with attachments
    console.log('2. Testing storeClobWithAttachments...');
    const stored = await service.storeClobWithAttachments(
      context,
      'Test CLOB content',
      testAttachments,
      'test-category',
      123
    );
    console.log('✓ Stored CLOB:', stored);

    // Test retrieve CLOB with attachments
    console.log('3. Testing retrieveClobWithAttachments...');
    const retrieved = await service.retrieveClobWithAttachments(
      context,
      stored.clobId,
      'test-category',
      123
    );
    console.log('✓ Retrieved CLOB:', { 
      contentLength: retrieved.content.length,
      attachmentCount: retrieved.attachments.length 
    });

    console.log('✓ ClobStorageService tests passed!');
  } catch (error) {
    console.error('✗ ClobStorageService test failed:', error.message);
  }
}

async function testMicroserviceContextService() {
  console.log('\n=== Testing MicroserviceContextService ===');
  
  const service = new MicroserviceContextService();
  const context = await createTestContext();

  try {
    // Test context validation
    console.log('1. Testing validateContext...');
    const validation = service.validateContext(context);
    console.log('✓ Context validation:', validation);

    // Test child context creation
    console.log('2. Testing createChildContext...');
    const childContext = service.createChildContext(context, { userId: 999 });
    console.log('✓ Child context created with userId:', childContext.userId);

    // Test transaction wrapper
    console.log('3. Testing withTransaction...');
    const result = await service.withTransaction(context, async (ctx) => {
      console.log('  - Inside transaction');
      return 'transaction-result';
    });
    console.log('✓ Transaction result:', result);

    // Test cache wrapper
    console.log('4. Testing withCache...');
    const cacheResult = await service.withCache(context, 'test-key', async () => {
      console.log('  - Cache miss, executing operation');
      return 'cached-value';
    });
    console.log('✓ Cache result:', cacheResult);

    // Test context logging
    console.log('5. Testing logContext...');
    service.logContext(context, 'test-operation');
    console.log('✓ Context logged');

    console.log('✓ MicroserviceContextService tests passed!');
  } catch (error) {
    console.error('✗ MicroserviceContextService test failed:', error.message);
  }
}

async function testFileSystemClient() {
  console.log('\n=== Testing File System Client ===');
  
  const context = await createTestContext();
  const fsClient = await context.fsClient;

  try {
    // Test file storage
    console.log('1. Testing updateEntityAttachments...');
    const fileIds = await fsClient.updateEntityAttachments(
      'test-category',
      456,
      testAttachments,
      { tenantId: 1, organisationGroupId: 1 }
    );
    console.log('✓ Stored files with IDs:', fileIds);

    // Test file retrieval
    console.log('2. Testing loadEntityAttachments...');
    const attachments = await fsClient.loadEntityAttachments('test-category', 456);
    console.log('✓ Retrieved attachments:', attachments.length, 'files');

    // Test individual file retrieval
    console.log('3. Testing loadEntityAttachmentByFileId...');
    if (fileIds.length > 0) {
      const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileIds[0]);
      console.log('✓ Retrieved file info:', fileInfo?.fileName);
    }

    console.log('✓ File System Client tests passed!');
  } catch (error) {
    console.error('✗ File System Client test failed:', error.message);
  }
}

// Run all tests
export async function runAllTests() {
  console.log('🧪 Starting EContentContent Implementation Tests...\n');
  
  await testEContentContentService();
  await testClobStorageService();
  await testMicroserviceContextService();
  await testFileSystemClient();
  
  console.log('\n🎉 All tests completed!');
}

// Export for use in other files
export {
  testEContentContentService,
  testClobStorageService,
  testMicroserviceContextService,
  testFileSystemClient,
};
