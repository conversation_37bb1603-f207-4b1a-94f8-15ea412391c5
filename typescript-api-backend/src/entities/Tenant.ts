import { DateTime, Entity, validators } from "edana-microservice";

import TenantInstitutionType from "../models/TenantInstitutionType";

import TenantPlatform from "../models/TenantPlatform";

import TenantStatus from "../models/TenantStatus";

const Tenant = new Entity<ITenant>(
  "Tenant",
  {
    domain: "string",
    clientAbbreviation: "string",
    siteLink: "string",
    licenseKey: "string",
    siteName: "string",
    logoFileId: "number",
    loginBgImgId: "number",
    appLoginBgImgId: "number",
    loginPageMessage: "string",
    createdAt: "date",
    status: {
      preset: "number",
      serialize: TenantStatus.serialize,
      deserialize: TenantStatus.deserialize,
      canUpdate: true,
    },
    platform: {
      columnName: "PLATFORM_ID",
      preset: "number",
      serialize: TenantPlatform.serialize,
      deserialize: TenantPlatform.deserialize,
      canUpdate: true,
    },
    institutionType: {
      columnName: "INSTITUTION_TYPE_ID",
      preset: "number",
      serialize: TenantInstitutionType.serialize,
      deserialize: TenantInstitutionType.deserialize,
      canUpdate: true,
    },
    countryId: "number",
    licenseName: "string",
  },
  {},
  {
    tableName: "C_TENANT",
    cache: {
      key: "Tenant",
      list: true,
      loader: async (connection) => await Tenant.loadAll(connection),
    },
  },
  {}
);

export default Tenant;

export interface ITenant {
  __type: "Tenant";
  id: number;
  domain?: string;
  siteName: string;
  clientAbbreviation: string;
  siteLink: string;
  licenseKey: string;
  licenseName?: string;
  logoFileId?: number;
  loginBgImgId?: number;
  appLoginBgImgId?: number;
  createdAt: DateTime | string;
  status: string;
  platform?: string;
  institutionType: string;
  countryId: number;
  loginPageMessage?: string;
}
