import { Get, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Req } from 'routing-controllers';
import { Service } from 'typedi';
import { Request, Response } from 'express';
import { getContext } from '../middleware/contextMiddleware';

@JsonController('/files')
@Service()
export class FilesController {
  


  @Get('/:fileId')
  async getFile(
    @Param('fileId') fileId: number,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const fsClient = await context.fsClient;
      
      // Get file metadata
      const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);
      
      if (!fileInfo) {
        return response.status(404).json({
          success: false,
          error: 'File not found',
        });
      }

      // For this mock implementation, we'll return file metadata
      // In a real implementation, you would stream the actual file content
      return response.json({
        success: true,
        data: {
          fileId: fileInfo.fileId,
          fileName: fileInfo.fileName,
          fileSize: fileInfo.fileSize,
          mimeType: fileInfo.mimeType,
          url: fileInfo.url,
        },
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Get('/:fileId/download')
  async downloadFile(
    @Param('fileId') fileId: number,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const fsClient = await context.fsClient;
      
      const fileInfo = await fsClient.loadEntityAttachmentByFileId(fileId);
      
      if (!fileInfo) {
        return response.status(404).json({
          success: false,
          error: 'File not found',
        });
      }

      // Set appropriate headers for file download
      response.setHeader('Content-Type', fileInfo.mimeType);
      response.setHeader('Content-Disposition', `attachment; filename="${fileInfo.fileName}"`);
      response.setHeader('Content-Length', fileInfo.fileSize);

      // For this mock implementation, return a simple text response
      // In a real implementation, you would stream the actual file
      return response.send(`Mock file content for ${fileInfo.fileName}`);
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }
}
