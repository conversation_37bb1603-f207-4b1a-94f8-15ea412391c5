import {
  Body,
  Get,
  Post,
  Put,
  Delete,
  JsonController,
  Param,
  QueryParams,
  Res,
  Req,
  UseBefore,
  UploadedFiles
} from 'routing-controllers';
import { Service } from 'typedi';
import { Request, Response } from 'express';
import multer from 'multer';
import { EContentContentService } from '../services/eContentContent.service';
import { IEContentContentCreate, IEContentContentUpdate } from '../interfaces/IEContentContent';
import { IContext } from 'edana-microservice';
import { IAttachFileInfo } from '../interfaces/IAttachFileInfo';
import { getContext } from '../middleware/contextMiddleware';
import { E_CONTENT_CONTENT_FILE } from '../constants/fsCategoriesAreas';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
});

// Middleware for file uploads
const fileUploadMiddleware = upload.array('files', 10);

@JsonController('/econtent')
@Service()
export class EContentContentController {
  constructor(private eContentContentService: EContentContentService) {}



  @Get('/contents')
  async getEContentContents(
    @QueryParams() query: {
      itemId?: number;
      resourceLanguageId?: number;
      first?: number;
      count?: number;
      searchQuery?: string;
    },
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const contents = await this.eContentContentService.getEContentContents(context, query);
      return response.json({
        success: true,
        data: contents,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Get('/contents/:id')
  async getEContentContent(
    @Param('id') id: number,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const content = await this.eContentContentService.getEContentContent(context, id);
      return response.json({
        success: true,
        data: content,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/contents/simple')
  async createEContentContentSimple(
    @Body() body: any,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const content = await this.eContentContentService.createEContentContent(context, body);

      return response.status(201).json({
        success: true,
        data: content,
        message: 'EContentContent created successfully (simple)',
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/contents')
  async createEContentContent(
    @Body() body: any,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);

      // Parse the data if it comes from multipart form
      let contentData = body;
      if (typeof body.data === 'string') {
        contentData = JSON.parse(body.data);
      }

      // Handle file uploads from request.files if available
      const files = (request as any).files || [];
      const attachments: IAttachFileInfo[] = files.map((file: any) => ({
        fileName: file.originalname || file.name,
        fileSize: file.size,
        mimeType: file.mimetype || file.type,
        content: file.buffer || file.data,
      }));

      // Add attachments to the first CLOB if files were uploaded
      if (attachments.length > 0 && contentData.clobs && contentData.clobs.length > 0) {
        contentData.clobs[0].attachments = attachments;
      }

      const content = await this.eContentContentService.createEContentContent(context, contentData);

      return response.status(201).json({
        success: true,
        data: content,
        message: 'EContentContent created successfully',
        filesUploaded: attachments.length,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Put('/contents/:id')
  @UseBefore(fileUploadMiddleware)
  async updateEContentContent(
    @Param('id') id: number,
    @Body() body: Omit<IEContentContentUpdate, 'id'>,
    @UploadedFiles('files') files: Express.Multer.File[],
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      
      // Process uploaded files
      const attachments: IAttachFileInfo[] = files ? files.map(file => ({
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype,
        content: file.buffer,
      })) : [];

      // Add attachments to the first CLOB if files were uploaded
      if (attachments.length > 0 && body.clobs && body.clobs.length > 0) {
        body.clobs[0].attachments = attachments;
      }

      const updateData: IEContentContentUpdate = { ...body, id };
      const content = await this.eContentContentService.updateEContentContent(context, updateData);
      
      return response.json({
        success: true,
        data: content,
        message: 'EContentContent updated successfully',
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Delete('/contents/:id')
  async deleteEContentContent(
    @Param('id') id: number,
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      await this.eContentContentService.deleteEContentContent(context, id);

      return response.json({
        success: true,
        message: 'EContentContent deleted successfully',
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/upload')
  @UseBefore(fileUploadMiddleware)
  async uploadFile(
    @UploadedFiles('files') files: Express.Multer.File[],
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);
      const fsClient = await context.fsClient;

      if (!files || files.length === 0) {
        return response.status(400).json({
          success: false,
          error: 'No files provided',
        });
      }

      // Prepare file information for upload initiation
      const fileInfos = files.map(file => ({
        fileId: null,
        tenantId: context.tenantId,
        organisationGroupId: context.organisationGroupId,
        fileName: file.originalname,
        mimeType: file.mimetype,
        fileSize: file.size,
      }));

      // Initiate upload and get upload tokens
      const uploadResponses = await fsClient.initiateAttachmentUpload(
        E_CONTENT_CONTENT_FILE,
        fileInfos
      );

      // Process the upload responses
      const results = uploadResponses.map((uploadResponse, index) => {
        if (uploadResponse.ok) {
          return {
            success: true,
            uploadToken: uploadResponse.uploadToken,
            uploadUrl: uploadResponse.uploadUrl,
            uploadTarget: uploadResponse.uploadTarget,
            fileName: files[index].originalname,
            fileSize: files[index].size,
            mimeType: files[index].mimetype,
          };
        } else {
          return {
            success: false,
            error: 'Failed to initiate upload',
            fileName: files[index].originalname,
          };
        }
      });

      // Check if any uploads failed
      const failedUploads = results.filter(result => !result.success);
      if (failedUploads.length > 0) {
        return response.status(400).json({
          success: false,
          error: 'Some file uploads failed to initiate',
          results,
        });
      }

      return response.status(200).json({
        success: true,
        message: 'File upload initiated successfully',
        data: results,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/upload/commit')
  async commitUpload(
    @Body() body: { uploadToken: string },
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      const context = getContext(request);

      if (!body.uploadToken) {
        return response.status(400).json({
          success: false,
          error: 'Upload token is required',
        });
      }

      // Use the service client to commit the upload
      const client = await context.requireServiceClient('edana_api_fs');
      const commitResponse = await client.upload.commit({
        body: {
          uploadToken: body.uploadToken,
          tenantId: context.tenantId
        },
      });

      if (commitResponse.ok) {
        return response.status(200).json({
          success: true,
          message: 'File upload committed successfully',
          data: {
            fileId: commitResponse.fileId,
            uploadToken: body.uploadToken,
            id: commitResponse.id,
            version: commitResponse.version,
          },
        });
      } else {
        return response.status(400).json({
          success: false,
          error: commitResponse.error || 'Failed to commit upload',
        });
      }
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/test-upload')
  @UseBefore(fileUploadMiddleware)
  async testUploadFile(
    @UploadedFiles('files') files: Express.Multer.File[],
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      if (!files || files.length === 0) {
        return response.status(400).json({
          success: false,
          error: 'No files provided',
        });
      }

      // Mock response for testing without authentication
      const results = files.map((file, index) => ({
        success: true,
        uploadToken: `mock-token-${Date.now()}-${index}`,
        uploadUrl: `mock-upload-url-${index}`,
        uploadTarget: 'app' as const,
        fileName: file.originalname,
        fileSize: file.size,
        mimeType: file.mimetype,
      }));

      return response.status(200).json({
        success: true,
        message: 'File upload initiated successfully (test mode)',
        data: results,
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  @Post('/test-commit')
  async testCommitUpload(
    @Body() body: { uploadToken: string },
    @Req() request: Request,
    @Res() response: Response
  ) {
    try {
      if (!body.uploadToken) {
        return response.status(400).json({
          success: false,
          error: 'Upload token is required',
        });
      }

      // Mock commit response for testing
      const commitResponse = {
        ok: true,
        fileId: Math.floor(Math.random() * 10000) + 1,
        id: Math.floor(Math.random() * 1000) + 1,
        version: 1,
      };

      return response.status(200).json({
        success: true,
        message: 'File upload committed successfully (test mode)',
        data: {
          fileId: commitResponse.fileId,
          uploadToken: body.uploadToken,
          id: commitResponse.id,
          version: commitResponse.version,
        },
      });
    } catch (error) {
      return response.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }
}
