# File Upload Endpoint Documentation

## Overview

This document describes the file upload endpoints that have been implemented using the fsClient and Edana FS microservice architecture.

## Endpoints

### 1. Upload File Endpoint

**URL:** `POST /api/econtent/upload`

**Description:** Initiates file upload and returns upload tokens and file IDs.

**Authentication:** Requires valid JWT token in Authorization header.

**Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Host: edana2-dev.ed-space.net" \
  -F "files=@your-file.txt" \
  http://localhost:3001/api/econtent/upload
```

**Response:**
```json
{
  "success": true,
  "message": "File upload initiated successfully",
  "data": [
    {
      "success": true,
      "uploadToken": "upload-token-123",
      "uploadUrl": "https://upload-url",
      "uploadTarget": "app",
      "fileName": "your-file.txt",
      "fileSize": 1024,
      "mimeType": "text/plain"
    }
  ]
}
```

### 2. Commit Upload Endpoint

**URL:** `POST /api/econtent/upload/commit`

**Description:** Commits the uploaded file and returns the final file ID.

**Authentication:** Requires valid JWT token in Authorization header.

**Request:**
```bash
curl -X POST \
  -H "Authorization: Bearer <JWT_TOKEN>" \
  -H "Content-Type: application/json" \
  -d '{"uploadToken": "upload-token-123"}' \
  http://localhost:3001/api/econtent/upload/commit
```

**Response:**
```json
{
  "success": true,
  "message": "File upload committed successfully",
  "data": {
    "fileId": 12345,
    "uploadToken": "upload-token-123",
    "id": 67890,
    "version": 1
  }
}
```

### 3. Test Endpoints (No Authentication)

For testing purposes, there are simplified endpoints that don't require authentication:

**Test Upload:** `POST /test/upload`
**Test Controller Upload:** `POST /api/econtent/test-upload`
**Test Commit:** `POST /api/econtent/test-commit`

## Implementation Details

### fsClient Methods Used

1. **`fsClient.initiateAttachmentUpload(fileCategoryAreaKey, files)`**
   - Initiates file upload process
   - Returns upload tokens and URLs
   - Uses file category area: `E_CONTENT_CONTENT_FILE`

2. **`fsClient.updateEntityAttachments(fileCategoryArea, referenceId, attachmentInputs, metaInfo)`**
   - Updates entity with file attachments
   - Commits upload tokens to file IDs

3. **Service Client `upload.commit({ body: { uploadToken, tenantId } })`**
   - Commits individual upload tokens
   - Returns final file metadata

### File Category Areas

The implementation uses the following file category area:
- `E_CONTENT_CONTENT_FILE = 'econtent-content-file'`

### Security Features

- JWT token validation with audience verification
- Tenant resolution from domain
- Signed cookie support for production
- File size limits (10MB default)
- MIME type validation

### Error Handling

The endpoints handle various error scenarios:
- Missing files
- Invalid upload tokens
- Authentication failures
- Database connection issues
- File size limits exceeded

## Usage Example

```typescript
// 1. Upload file and get upload token
const uploadResponse = await fetch('/api/econtent/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  },
  body: formData
});

const uploadResult = await uploadResponse.json();
const uploadToken = uploadResult.data[0].uploadToken;

// 2. Commit the upload to get file ID
const commitResponse = await fetch('/api/econtent/upload/commit', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({ uploadToken })
});

const commitResult = await commitResponse.json();
const fileId = commitResult.data.fileId;
```

## Notes

- The implementation includes both production-ready authenticated endpoints and test endpoints for development
- Mock implementations are provided for testing when database connections are not available
- The file upload process follows the standard Edana FS pattern: initiate → upload → commit
- File category areas must be properly configured in the database for production use
